# Privacy Policy

## Overview

This privacy policy explains how data is handled when using the n8n Self-Host Installer. Since this is a self-hosted solution, you maintain full control over your data.

## Data Controller

**You are the data controller** when using this installer. This means:
- You decide what data to collect and process
- You are responsible for compliance with privacy laws
- You control data retention and deletion
- You manage user consent and rights

## Data We Don't Collect

The n8n Self-Host Installer itself does not collect, store, or transmit any personal data to external services, except as explicitly configured by you.

### No Telemetry
- No usage analytics sent to developers
- No crash reports or error tracking
- No performance metrics collection
- No automatic updates that phone home

### No Third-Party Tracking
- No Google Analytics or similar services
- No social media tracking pixels
- No advertising networks
- No external CDNs for tracking

## Data You Control

### n8n Application Data
Your n8n instance may process:
- **Workflow data**: Automation logic and configurations
- **Execution data**: Workflow run history and results
- **User accounts**: Admin and user credentials
- **Webhook data**: External data received via webhooks
- **Integration data**: Data from connected services

**Storage**: All data stored locally on your server or in your configured database.

### System Data
The installer creates:
- **Log files**: Deployment and application logs
- **Configuration files**: Docker and service configurations
- **SSL certificates**: Let's Encrypt certificates for your domain
- **Backup files**: Encrypted backups of your n8n data

**Storage**: Stored on your server and optionally in your Google Drive.

### GitHub Actions Data
During deployment, GitHub Actions may access:
- **Repository code**: Public repository content
- **Secrets**: Encrypted secrets you configure
- **Workflow logs**: Deployment execution logs

**Storage**: GitHub's infrastructure (see GitHub's privacy policy).

## Third-Party Services

### Required Services
These services are necessary for the installer to function:

**Docker Hub**
- Purpose: Download container images
- Data: IP address, download requests
- Privacy Policy: https://www.docker.com/legal/privacy

**Let's Encrypt**
- Purpose: SSL certificate issuance
- Data: Domain name, IP address
- Privacy Policy: https://letsencrypt.org/privacy/

**Ubuntu Package Repositories**
- Purpose: Software package downloads
- Data: IP address, package requests
- Privacy Policy: https://ubuntu.com/legal/data-privacy

### Optional Services
These services are only used if you configure them:

**Google Drive (for backups)**
- Purpose: Backup storage
- Data: Encrypted backup files
- Privacy Policy: https://policies.google.com/privacy

**GitHub (for deployment)**
- Purpose: CI/CD automation
- Data: Repository content, workflow logs
- Privacy Policy: https://docs.github.com/en/site-policy/privacy-policies/github-privacy-statement

## Data Processing Activities

### Backup Processing
- **Purpose**: Disaster recovery and data protection
- **Data**: Complete n8n database and file backups
- **Processing**: Compression, encryption, upload to Google Drive
- **Retention**: 30 days by default (configurable)
- **Legal Basis**: Legitimate interest in data protection

### Log Processing
- **Purpose**: System monitoring and troubleshooting
- **Data**: Application logs, system metrics
- **Processing**: Collection, rotation, local storage
- **Retention**: 30 days by default
- **Legal Basis**: Legitimate interest in system operation

### SSL Certificate Processing
- **Purpose**: Secure HTTPS connections
- **Data**: Domain name, server IP address
- **Processing**: Certificate request and validation
- **Retention**: Certificate lifetime (90 days)
- **Legal Basis**: Legitimate interest in security

## Your Responsibilities

As the data controller, you must:

### Compliance
- Comply with applicable privacy laws (GDPR, CCPA, etc.)
- Implement appropriate technical and organizational measures
- Conduct privacy impact assessments when required
- Maintain records of processing activities

### User Rights
- Provide privacy notices to your users
- Handle data subject requests (access, rectification, deletion)
- Obtain necessary consents for data processing
- Implement data portability mechanisms

### Data Security
- Secure your server and database
- Use strong passwords and authentication
- Keep software updated
- Monitor for security incidents

## Data Retention

### Default Retention Periods
- **n8n execution data**: Configurable in n8n settings
- **Application logs**: 30 days (configurable)
- **Backup files**: 30 days (configurable)
- **SSL certificates**: 90 days (auto-renewed)

### Configuring Retention
You can modify retention periods by:
- Editing Docker Compose environment variables
- Configuring logrotate settings
- Modifying backup script parameters
- Setting n8n execution data retention

## Data Deletion

### Manual Deletion
- **n8n data**: Use n8n's built-in data management
- **Backups**: Delete from Google Drive manually or via script
- **Logs**: Remove from `/var/log/` directories
- **Complete removal**: Destroy VM and all associated storage

### Automated Deletion
- **Log rotation**: Automatic cleanup of old log files
- **Backup rotation**: Automatic removal of old backups
- **Temporary files**: Automatic cleanup during deployments

## International Data Transfers

### Server Location
Your data remains in the geographic region where you deploy your server. Choose your server location based on:
- User location and latency requirements
- Data residency requirements
- Applicable privacy laws

### Backup Location
Google Drive backups may be stored in Google's global infrastructure. Consider:
- Google's data center locations
- Your data residency requirements
- Alternative backup solutions if needed

## Privacy by Design

This installer implements privacy by design principles:

### Data Minimization
- Only necessary data is processed
- No unnecessary logging or tracking
- Configurable retention periods

### Purpose Limitation
- Data used only for stated purposes
- No secondary use without consent
- Clear documentation of processing activities

### Storage Limitation
- Automatic data deletion after retention periods
- Configurable retention settings
- No indefinite data storage

### Security
- Encryption in transit and at rest
- Access controls and authentication
- Regular security updates

## Changes to This Policy

We may update this privacy policy to reflect:
- Changes in applicable laws
- New features or services
- Feedback from the community
- Security improvements

Changes will be communicated through:
- GitHub repository updates
- Release notes
- Community discussions

## Contact Information

For privacy-related questions:
- **GitHub Issues**: Use "privacy" label
- **Email**: [<EMAIL>]
- **Community**: GitHub Discussions

## Legal Disclaimers

### No Warranty
This installer is provided "as is" without warranties regarding privacy or data protection compliance.

### Your Responsibility
You are solely responsible for:
- Compliance with applicable privacy laws
- Proper configuration and security
- User consent and data subject rights
- Data breach notification and response

### Limitation of Liability
The installer developers are not liable for:
- Privacy law violations
- Data breaches or security incidents
- Misuse or misconfiguration
- Third-party service privacy practices

---

**Important**: This privacy policy covers the installer itself. You must create your own privacy policy for your n8n instance and any data processing you perform.
