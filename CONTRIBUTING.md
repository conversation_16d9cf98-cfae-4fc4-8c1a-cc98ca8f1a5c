# Contributing to n8n Self-Host Installer

Thank you for your interest in contributing! This project aims to make n8n self-hosting accessible and secure for everyone.

## 🤝 How to Contribute

### Reporting Issues
- Use GitHub Issues for bug reports and feature requests
- Search existing issues before creating new ones
- Use issue templates when available
- Provide detailed reproduction steps

### Suggesting Features
- Open a GitHub Discussion for feature ideas
- Explain the use case and benefits
- Consider implementation complexity
- Check if it aligns with project goals

### Contributing Code
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 🛠️ Development Setup

### Prerequisites
- Ubuntu 22.04 VM for testing
- Docker and Docker Compose
- GitHub account with Actions enabled
- Basic knowledge of <PERSON><PERSON>, Docker, and GitHub Actions

### Local Testing
```bash
# Clone your fork
git clone https://github.com/yourusername/n8n-self-host-installer.git
cd n8n-self-host-installer

# Test scripts locally (requires VM)
./scripts/setup_vm.sh --dry-run
./scripts/deploy_n8n.sh --test

# Validate Docker Compose
docker-compose -f docker/docker-compose.yml config

# Check shell scripts
shellcheck scripts/*.sh
```

### Testing Changes
1. **Script Testing**: Test all modified scripts on a clean VM
2. **Workflow Testing**: Run GitHub Actions workflows in your fork
3. **Integration Testing**: Complete end-to-end deployment
4. **Rollback Testing**: Verify rollback procedures work

## 📋 Contribution Guidelines

### Code Style

**Bash Scripts**
- Use `set -euo pipefail` at the start
- Quote variables: `"$variable"`
- Use meaningful function names
- Add error handling and logging
- Follow Google Shell Style Guide

**Docker Compose**
- Use version 3.8+ format
- Include health checks
- Set resource limits
- Use environment variables
- Add restart policies

**GitHub Actions**
- Pin action versions (not @main)
- Use minimal permissions
- Add timeout limits
- Include error handling
- Document workflow inputs

### Documentation
- Update README.md for new features
- Add inline comments for complex logic
- Update QUICKSTART.md for user-facing changes
- Include security considerations
- Provide examples and use cases

### Security Requirements
- Never commit secrets or credentials
- Use secure defaults
- Validate all inputs
- Follow principle of least privilege
- Document security implications

## 🧪 Testing Standards

### Required Tests
- **Script Tests**: All scripts must be idempotent
- **Integration Tests**: Full deployment pipeline
- **Security Tests**: Verify security configurations
- **Rollback Tests**: Ensure safe rollback procedures

### Test Environments
- **Development**: Local VM testing
- **Staging**: Fork with test secrets
- **Production**: Real deployment validation

### Test Checklist
- [ ] Scripts run without errors
- [ ] Idempotent execution works
- [ ] Error handling functions correctly
- [ ] Logs are informative
- [ ] Security configurations applied
- [ ] Backup and restore work
- [ ] SSL certificates obtained
- [ ] n8n accessible and functional

## 🔄 Pull Request Process

### Before Submitting
1. Test changes on clean VM
2. Update documentation
3. Add/update tests
4. Check for security issues
5. Verify backward compatibility

### PR Requirements
- Clear description of changes
- Link to related issues
- Include test results
- Update CHANGELOG.md
- Pass all CI checks

### Review Process
1. Automated checks must pass
2. Code review by maintainers
3. Security review for sensitive changes
4. Integration testing
5. Documentation review

## 🏷️ Issue Labels

### Type Labels
- `bug` - Something isn't working
- `enhancement` - New feature or improvement
- `documentation` - Documentation updates
- `security` - Security-related issues
- `question` - Questions about usage

### Priority Labels
- `critical` - Security issues, data loss
- `high` - Major functionality broken
- `medium` - Important improvements
- `low` - Nice to have features

### Status Labels
- `needs-triage` - Needs initial review
- `needs-info` - More information required
- `in-progress` - Being worked on
- `ready-for-review` - Ready for PR review

## 🎯 Project Goals

### Primary Goals
- **Simplicity**: One-click deployment
- **Security**: Production-ready security
- **Reliability**: Stable, tested deployments
- **Documentation**: Clear, comprehensive guides

### Non-Goals
- Multi-server deployments
- Kubernetes support
- Custom n8n modifications
- Commercial support

## 🚀 Release Process

### Version Numbering
- Follow Semantic Versioning (SemVer)
- Major: Breaking changes
- Minor: New features, backward compatible
- Patch: Bug fixes, security updates

### Release Checklist
- [ ] All tests pass
- [ ] Documentation updated
- [ ] CHANGELOG.md updated
- [ ] Security review completed
- [ ] Backward compatibility verified
- [ ] Release notes prepared

## 🛡️ Security Contributions

### Security Issues
- Report privately via email
- Allow 90 days for disclosure
- Provide detailed reproduction steps
- Suggest mitigation strategies

### Security Reviews
- All PRs reviewed for security impact
- Secrets and credentials never committed
- Dependencies regularly updated
- Security best practices followed

## 📞 Getting Help

### Community Support
- **GitHub Discussions**: General questions and ideas
- **GitHub Issues**: Bug reports and feature requests
- **Discord/Slack**: Real-time community chat (if available)

### Maintainer Contact
- **GitHub**: @maintainer-username
- **Email**: <EMAIL>
- **Response Time**: 48-72 hours typical

## 🙏 Recognition

### Contributors
All contributors are recognized in:
- GitHub Contributors page
- CHANGELOG.md acknowledgments
- README.md contributors section

### Types of Contributions
- Code contributions
- Documentation improvements
- Bug reports and testing
- Feature suggestions
- Community support

## 📜 Code of Conduct

### Our Standards
- Be respectful and inclusive
- Focus on constructive feedback
- Help others learn and grow
- Maintain professional communication

### Unacceptable Behavior
- Harassment or discrimination
- Trolling or inflammatory comments
- Personal attacks
- Sharing private information

### Enforcement
- Issues reported to maintainers
- Warnings for minor violations
- Temporary or permanent bans for serious violations
- Appeals process available

## 📄 License

By contributing, you agree that your contributions will be licensed under the same license as the project (MIT License).

---

**Thank you for contributing!** Your help makes n8n self-hosting better for everyone. 🎉
