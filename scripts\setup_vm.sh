#!/bin/bash

# n8n Self-Host Installer - VM Setup Script
# This script sets up a Ubuntu server for n8n deployment
# It's designed to be idempotent - safe to run multiple times

set -euo pipefail

# =============================================================================
# CONFIGURATION
# =============================================================================

SCRIPT_NAME="setup_vm.sh"
LOG_FILE="/var/log/n8n-setup.log"
DEPLOY_USER="deployer"
N8N_DIR="/opt/n8n"
BACKUP_DIR="/var/backups/n8n"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================

log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

log_info() {
    log "INFO" "$@"
    echo -e "${BLUE}[INFO]${NC} $*"
}

log_success() {
    log "SUCCESS" "$@"
    echo -e "${GREEN}[SUCCESS]${NC} $*"
}

log_warning() {
    log "WARNING" "$@"
    echo -e "${YELLOW}[WARNING]${NC} $*"
}

log_error() {
    log "ERROR" "$@"
    echo -e "${RED}[ERROR]${NC} $*" >&2
}

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "This script should not be run as root"
        log_error "Please run as a regular user with sudo privileges"
        exit 1
    fi
}

check_ubuntu() {
    if ! grep -q "Ubuntu" /etc/os-release; then
        log_error "This script is designed for Ubuntu. Detected:"
        cat /etc/os-release | grep PRETTY_NAME
        log_error "Please use Ubuntu 22.04 LTS or later"
        exit 1
    fi
    
    local version=$(grep VERSION_ID /etc/os-release | cut -d'"' -f2)
    log_info "Detected Ubuntu version: $version"
    
    if [[ $(echo "$version >= 22.04" | bc -l) -eq 0 ]]; then
        log_warning "Ubuntu version $version detected. Recommended: 22.04 LTS or later"
    fi
}

check_internet() {
    log_info "Checking internet connectivity..."
    if ! ping -c 1 ******* &> /dev/null; then
        log_error "No internet connectivity detected"
        log_error "Please ensure the server has internet access"
        exit 1
    fi
    log_success "Internet connectivity confirmed"
}

check_resources() {
    log_info "Checking system resources..."
    
    # Check memory (minimum 1GB)
    local mem_gb=$(free -g | awk '/^Mem:/{print $2}')
    if [[ $mem_gb -lt 1 ]]; then
        log_error "Insufficient memory: ${mem_gb}GB detected, minimum 1GB required"
        exit 1
    fi
    log_info "Memory: ${mem_gb}GB available"
    
    # Check disk space (minimum 10GB free)
    local disk_gb=$(df / | awk 'NR==2{print int($4/1024/1024)}')
    if [[ $disk_gb -lt 10 ]]; then
        log_error "Insufficient disk space: ${disk_gb}GB free, minimum 10GB required"
        exit 1
    fi
    log_info "Disk space: ${disk_gb}GB available"
}

# =============================================================================
# SYSTEM UPDATE FUNCTIONS
# =============================================================================

update_system() {
    log_info "Updating system packages..."
    
    # Update package lists
    sudo apt-get update -y
    
    # Upgrade existing packages
    sudo DEBIAN_FRONTEND=noninteractive apt-get upgrade -y
    
    # Install essential packages
    sudo DEBIAN_FRONTEND=noninteractive apt-get install -y \
        curl \
        wget \
        git \
        unzip \
        software-properties-common \
        apt-transport-https \
        ca-certificates \
        gnupg \
        lsb-release \
        ufw \
        fail2ban \
        htop \
        nano \
        vim \
        tree \
        jq \
        bc
    
    log_success "System packages updated"
}

# =============================================================================
# USER MANAGEMENT FUNCTIONS
# =============================================================================

setup_deploy_user() {
    log_info "Setting up deploy user: $DEPLOY_USER"
    
    # Check if user already exists
    if id "$DEPLOY_USER" &>/dev/null; then
        log_info "Deploy user $DEPLOY_USER already exists"
    else
        # Create deploy user
        sudo adduser --disabled-password --gecos "" "$DEPLOY_USER"
        log_success "Created deploy user: $DEPLOY_USER"
    fi
    
    # Add to docker group (will be created later)
    sudo usermod -aG sudo "$DEPLOY_USER" || true
    
    # Setup SSH directory
    sudo mkdir -p "/home/<USER>/.ssh"
    sudo chmod 700 "/home/<USER>/.ssh"
    
    # Copy current user's authorized_keys if it exists
    if [[ -f "$HOME/.ssh/authorized_keys" ]]; then
        sudo cp "$HOME/.ssh/authorized_keys" "/home/<USER>/.ssh/"
        sudo chown -R "$DEPLOY_USER:$DEPLOY_USER" "/home/<USER>/.ssh"
        sudo chmod 600 "/home/<USER>/.ssh/authorized_keys"
        log_success "SSH keys copied to deploy user"
    else
        log_warning "No SSH keys found to copy. You'll need to add them manually."
    fi
}

# =============================================================================
# DOCKER INSTALLATION FUNCTIONS
# =============================================================================

install_docker() {
    log_info "Installing Docker..."
    
    # Check if Docker is already installed
    if command -v docker &> /dev/null; then
        log_info "Docker is already installed"
        docker --version
        return 0
    fi
    
    # Remove old versions
    sudo apt-get remove -y docker docker-engine docker.io containerd runc || true
    
    # Add Docker's official GPG key
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    
    # Add Docker repository
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # Update package lists
    sudo apt-get update -y
    
    # Install Docker
    sudo DEBIAN_FRONTEND=noninteractive apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
    
    # Start and enable Docker
    sudo systemctl start docker
    sudo systemctl enable docker
    
    # Add users to docker group
    sudo usermod -aG docker "$USER"
    sudo usermod -aG docker "$DEPLOY_USER"
    
    log_success "Docker installed successfully"
    docker --version
}

install_docker_compose() {
    log_info "Installing Docker Compose..."
    
    # Check if docker-compose is already available
    if docker compose version &> /dev/null; then
        log_info "Docker Compose (plugin) is already available"
        docker compose version
        return 0
    fi
    
    # Install standalone docker-compose as fallback
    local compose_version="2.24.0"
    sudo curl -L "https://github.com/docker/compose/releases/download/v${compose_version}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
    
    log_success "Docker Compose installed successfully"
    docker-compose --version || docker compose version
}

# =============================================================================
# ADDITIONAL TOOLS INSTALLATION
# =============================================================================

install_rclone() {
    log_info "Installing rclone..."
    
    # Check if rclone is already installed
    if command -v rclone &> /dev/null; then
        log_info "rclone is already installed"
        rclone --version | head -1
        return 0
    fi
    
    # Install rclone
    curl https://rclone.org/install.sh | sudo bash
    
    log_success "rclone installed successfully"
    rclone --version | head -1
}

install_certbot() {
    log_info "Installing Certbot..."
    
    # Check if certbot is already installed
    if command -v certbot &> /dev/null; then
        log_info "Certbot is already installed"
        certbot --version
        return 0
    fi
    
    # Install certbot
    sudo DEBIAN_FRONTEND=noninteractive apt-get install -y certbot python3-certbot-nginx
    
    log_success "Certbot installed successfully"
    certbot --version
}

# =============================================================================
# FIREWALL CONFIGURATION
# =============================================================================

configure_firewall() {
    log_info "Configuring UFW firewall..."
    
    # Reset UFW to defaults
    sudo ufw --force reset
    
    # Set default policies
    sudo ufw default deny incoming
    sudo ufw default allow outgoing
    
    # Allow SSH (be careful not to lock yourself out)
    sudo ufw allow ssh
    sudo ufw allow 22/tcp
    
    # Allow HTTP and HTTPS
    sudo ufw allow 80/tcp
    sudo ufw allow 443/tcp
    
    # Enable UFW
    sudo ufw --force enable
    
    # Show status
    sudo ufw status verbose
    
    log_success "Firewall configured successfully"
}

# =============================================================================
# DIRECTORY SETUP
# =============================================================================

setup_directories() {
    log_info "Setting up directories..."
    
    # Create main directories
    sudo mkdir -p "$N8N_DIR"
    sudo mkdir -p "$BACKUP_DIR"
    sudo mkdir -p "/var/log/n8n"
    
    # Set ownership
    sudo chown -R "$DEPLOY_USER:$DEPLOY_USER" "$N8N_DIR"
    sudo chown -R "$DEPLOY_USER:$DEPLOY_USER" "$BACKUP_DIR"
    sudo chown -R "$DEPLOY_USER:$DEPLOY_USER" "/var/log/n8n"
    
    # Set permissions
    sudo chmod 755 "$N8N_DIR"
    sudo chmod 755 "$BACKUP_DIR"
    sudo chmod 755 "/var/log/n8n"
    
    log_success "Directories created successfully"
}

# =============================================================================
# SECURITY HARDENING
# =============================================================================

configure_fail2ban() {
    log_info "Configuring Fail2Ban..."
    
    # Create custom SSH jail configuration
    sudo tee /etc/fail2ban/jail.local > /dev/null <<EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
maxretry = 3
bantime = 3600
EOF
    
    # Restart and enable fail2ban
    sudo systemctl restart fail2ban
    sudo systemctl enable fail2ban
    
    log_success "Fail2Ban configured successfully"
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================

main() {
    log_info "Starting n8n VM setup..."
    log_info "Script: $SCRIPT_NAME"
    log_info "User: $(whoami)"
    log_info "Date: $(date)"
    
    # Pre-flight checks
    check_root
    check_ubuntu
    check_internet
    check_resources
    
    # System setup
    update_system
    setup_deploy_user
    
    # Install software
    install_docker
    install_docker_compose
    install_rclone
    install_certbot
    
    # Configure security
    configure_firewall
    configure_fail2ban
    
    # Setup directories
    setup_directories
    
    log_success "VM setup completed successfully!"
    log_info "Next steps:"
    log_info "1. Configure rclone for Google Drive backups"
    log_info "2. Run deploy_n8n.sh to install n8n"
    log_info "3. Run configure_proxy.sh to set up NGINX and SSL"
    
    # Display system information
    echo ""
    echo "=== System Information ==="
    echo "OS: $(grep PRETTY_NAME /etc/os-release | cut -d'"' -f2)"
    echo "Memory: $(free -h | grep Mem | awk '{print $2}')"
    echo "Disk: $(df -h / | awk 'NR==2{print $4}') free"
    echo "Docker: $(docker --version)"
    echo "Docker Compose: $(docker compose version --short 2>/dev/null || docker-compose --version)"
    echo "rclone: $(rclone --version | head -1)"
    echo "Certbot: $(certbot --version)"
    echo ""
    
    log_info "Setup log saved to: $LOG_FILE"
}

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================

# Handle command line arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [--help|--dry-run]"
        echo "  --help     Show this help message"
        echo "  --dry-run  Show what would be done without making changes"
        exit 0
        ;;
    --dry-run)
        echo "DRY RUN MODE - No changes will be made"
        echo "This would set up the VM for n8n deployment"
        exit 0
        ;;
esac

# Create log directory if it doesn't exist
sudo mkdir -p "$(dirname "$LOG_FILE")"
sudo touch "$LOG_FILE"
sudo chmod 644 "$LOG_FILE"

# Run main function
main "$@"
