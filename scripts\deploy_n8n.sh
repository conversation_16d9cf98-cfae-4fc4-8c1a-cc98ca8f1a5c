#!/bin/bash

# n8n Self-Host Installer - Deployment Script
# This script deploys n8n using Docker Compose with health checks and logging
# It's designed to be idempotent - safe to run multiple times

set -euo pipefail

# =============================================================================
# CONFIGURATION
# =============================================================================

SCRIPT_NAME="deploy_n8n.sh"
LOG_FILE="/var/log/n8n/deploy.log"
N8N_DIR="/opt/n8n"
COMPOSE_FILE="$N8N_DIR/docker-compose.yml"
ENV_FILE="$N8N_DIR/.env"
BACKUP_DIR="/var/backups/n8n"

# Health check settings
HEALTH_CHECK_TIMEOUT=300  # 5 minutes
HEALTH_CHECK_INTERVAL=10  # 10 seconds
MAX_RETRIES=30

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================

log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

log_info() {
    log "INFO" "$@"
    echo -e "${BLUE}[INFO]${NC} $*"
}

log_success() {
    log "SUCCESS" "$@"
    echo -e "${GREEN}[SUCCESS]${NC} $*"
}

log_warning() {
    log "WARNING" "$@"
    echo -e "${YELLOW}[WARNING]${NC} $*"
}

log_error() {
    log "ERROR" "$@"
    echo -e "${RED}[ERROR]${NC} $*" >&2
}

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if running as correct user
    if [[ $EUID -eq 0 ]]; then
        log_error "This script should not be run as root"
        exit 1
    fi
    
    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker is not running or user doesn't have permission"
        log_error "Try: sudo usermod -aG docker $USER && newgrp docker"
        exit 1
    fi
    
    # Check if Docker Compose is available
    if ! docker compose version &> /dev/null && ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not available"
        exit 1
    fi
    
    # Check if n8n directory exists
    if [[ ! -d "$N8N_DIR" ]]; then
        log_error "n8n directory not found: $N8N_DIR"
        log_error "Please run setup_vm.sh first"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

setup_directories() {
    log_info "Setting up directories..."
    
    # Create required directories
    mkdir -p "$N8N_DIR/data/postgres"
    mkdir -p "$N8N_DIR/data/redis"
    mkdir -p "$N8N_DIR/data/n8n"
    mkdir -p "$N8N_DIR/data/uptime-kuma"
    mkdir -p "$BACKUP_DIR"
    mkdir -p "/var/log/n8n"
    mkdir -p "/var/log/nginx"
    
    # Set proper permissions
    chmod 755 "$N8N_DIR/data"
    chmod 755 "$N8N_DIR/data/postgres"
    chmod 755 "$N8N_DIR/data/redis"
    chmod 755 "$N8N_DIR/data/n8n"
    chmod 755 "$N8N_DIR/data/uptime-kuma"
    
    log_success "Directories set up successfully"
}

copy_docker_files() {
    log_info "Copying Docker configuration files..."
    
    # Copy docker-compose.yml
    if [[ -f "docker/docker-compose.yml" ]]; then
        cp "docker/docker-compose.yml" "$COMPOSE_FILE"
        log_info "Copied docker-compose.yml"
    else
        log_error "docker-compose.yml not found in docker/ directory"
        exit 1
    fi
    
    # Copy NGINX configuration
    if [[ -d "docker/nginx" ]]; then
        cp -r "docker/nginx" "$N8N_DIR/"
        log_info "Copied NGINX configuration"
    else
        log_warning "NGINX configuration not found, will be created by configure_proxy.sh"
    fi
    
    log_success "Docker files copied successfully"
}

validate_env_file() {
    log_info "Validating environment file..."
    
    if [[ ! -f "$ENV_FILE" ]]; then
        log_error "Environment file not found: $ENV_FILE"
        log_error "Please copy .env.example to $ENV_FILE and configure it"
        exit 1
    fi
    
    # Check for required variables
    local required_vars=(
        "N8N_ENCRYPTION_KEY"
        "DB_POSTGRESDB_PASSWORD"
        "DOMAIN_NAME"
    )
    
    for var in "${required_vars[@]}"; do
        if ! grep -q "^${var}=" "$ENV_FILE" || grep -q "^${var}=$" "$ENV_FILE" || grep -q "^${var}=CHANGE" "$ENV_FILE"; then
            log_error "Required environment variable $var is not set or has default value"
            log_error "Please configure $ENV_FILE properly"
            exit 1
        fi
    done
    
    log_success "Environment file validation passed"
}

pull_images() {
    log_info "Pulling Docker images..."
    
    cd "$N8N_DIR"
    
    # Pull images
    if docker compose version &> /dev/null; then
        docker compose pull
    else
        docker-compose pull
    fi
    
    log_success "Docker images pulled successfully"
}

start_services() {
    log_info "Starting services..."
    
    cd "$N8N_DIR"
    
    # Start services
    if docker compose version &> /dev/null; then
        docker compose up -d --remove-orphans
    else
        docker-compose up -d --remove-orphans
    fi
    
    log_success "Services started successfully"
}

wait_for_health() {
    local service="$1"
    local max_attempts="$2"
    local attempt=1
    
    log_info "Waiting for $service to be healthy..."
    
    while [[ $attempt -le $max_attempts ]]; do
        if docker compose version &> /dev/null; then
            if docker compose ps "$service" | grep -q "healthy"; then
                log_success "$service is healthy"
                return 0
            fi
        else
            if docker-compose ps "$service" | grep -q "healthy"; then
                log_success "$service is healthy"
                return 0
            fi
        fi
        
        log_info "Attempt $attempt/$max_attempts: $service not ready yet, waiting..."
        sleep "$HEALTH_CHECK_INTERVAL"
        ((attempt++))
    done
    
    log_error "$service failed to become healthy within timeout"
    return 1
}

check_n8n_health() {
    log_info "Checking n8n application health..."
    
    local max_attempts=$((HEALTH_CHECK_TIMEOUT / HEALTH_CHECK_INTERVAL))
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f -s http://localhost:5678/healthz > /dev/null 2>&1; then
            log_success "n8n application is responding"
            return 0
        fi
        
        log_info "Attempt $attempt/$max_attempts: n8n not responding yet, waiting..."
        sleep "$HEALTH_CHECK_INTERVAL"
        ((attempt++))
    done
    
    log_error "n8n application failed to respond within timeout"
    return 1
}

show_status() {
    log_info "Showing service status..."
    
    cd "$N8N_DIR"
    
    echo ""
    echo "=== Docker Compose Services ==="
    if docker compose version &> /dev/null; then
        docker compose ps
    else
        docker-compose ps
    fi
    
    echo ""
    echo "=== Container Health Status ==="
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    echo ""
    echo "=== Resource Usage ==="
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"
}

create_backup() {
    log_info "Creating pre-deployment backup..."
    
    local backup_name="pre-deploy-$(date +%Y%m%d-%H%M%S)"
    local backup_path="$BACKUP_DIR/$backup_name"
    
    mkdir -p "$backup_path"
    
    # Backup n8n data if it exists
    if [[ -d "$N8N_DIR/data/n8n" ]] && [[ "$(ls -A $N8N_DIR/data/n8n)" ]]; then
        tar -czf "$backup_path/n8n-data.tar.gz" -C "$N8N_DIR/data" n8n/
        log_info "n8n data backed up to $backup_path/n8n-data.tar.gz"
    fi
    
    # Backup environment file
    if [[ -f "$ENV_FILE" ]]; then
        cp "$ENV_FILE" "$backup_path/.env"
        log_info "Environment file backed up"
    fi
    
    # Backup docker-compose file
    if [[ -f "$COMPOSE_FILE" ]]; then
        cp "$COMPOSE_FILE" "$backup_path/docker-compose.yml"
        log_info "Docker Compose file backed up"
    fi
    
    log_success "Pre-deployment backup created: $backup_path"
}

# =============================================================================
# MAIN DEPLOYMENT FUNCTION
# =============================================================================

deploy() {
    log_info "Starting n8n deployment..."
    log_info "Timestamp: $(date)"
    log_info "User: $(whoami)"
    log_info "Directory: $(pwd)"
    
    # Pre-deployment steps
    check_prerequisites
    setup_directories
    copy_docker_files
    validate_env_file
    create_backup
    
    # Deployment steps
    pull_images
    start_services
    
    # Health checks
    cd "$N8N_DIR"
    
    log_info "Waiting for services to start..."
    sleep 10
    
    # Wait for database
    if ! wait_for_health "postgres" "$MAX_RETRIES"; then
        log_error "PostgreSQL failed to start"
        show_status
        exit 1
    fi
    
    # Wait for Redis
    if ! wait_for_health "redis" "$MAX_RETRIES"; then
        log_error "Redis failed to start"
        show_status
        exit 1
    fi
    
    # Wait for n8n
    if ! wait_for_health "n8n" "$MAX_RETRIES"; then
        log_error "n8n failed to start"
        show_status
        exit 1
    fi
    
    # Check n8n application health
    if ! check_n8n_health; then
        log_error "n8n application health check failed"
        show_status
        exit 1
    fi
    
    # Show final status
    show_status
    
    log_success "n8n deployment completed successfully!"
    log_info "n8n is available at: http://localhost:5678"
    log_info "Configure NGINX proxy with: ./scripts/configure_proxy.sh"
}

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================

# Handle command line arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [--help|--test|--status|--logs]"
        echo "  --help     Show this help message"
        echo "  --test     Test deployment without making changes"
        echo "  --status   Show current service status"
        echo "  --logs     Show service logs"
        exit 0
        ;;
    --test)
        echo "TEST MODE - Checking deployment readiness"
        check_prerequisites
        validate_env_file
        echo "Deployment test passed"
        exit 0
        ;;
    --status)
        cd "$N8N_DIR" 2>/dev/null || { echo "n8n not deployed yet"; exit 1; }
        show_status
        exit 0
        ;;
    --logs)
        cd "$N8N_DIR" 2>/dev/null || { echo "n8n not deployed yet"; exit 1; }
        if docker compose version &> /dev/null; then
            docker compose logs -f
        else
            docker-compose logs -f
        fi
        exit 0
        ;;
esac

# Create log directory if it doesn't exist
mkdir -p "$(dirname "$LOG_FILE")"
touch "$LOG_FILE"

# Run deployment
deploy "$@"
