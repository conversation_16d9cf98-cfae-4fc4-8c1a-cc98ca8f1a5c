# Changelog

All notable changes to the n8n Self-Host Installer will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Initial release of n8n Self-Host Installer
- One-click deployment via GitHub Actions
- Automated VM setup with Docker and security hardening
- NGINX reverse proxy with Let's Encrypt SSL
- PostgreSQL and Redis integration
- Automated daily backups to Google Drive with rclone
- Backup restoration functionality
- Optional monitoring with Beszel and Uptime-Kuma
- Sample n8n workflows (volunteer signup, incident report, donation tracking)
- Comprehensive documentation and security guidelines

### Security
- SSH key-based authentication only
- UFW firewall configuration
- Encrypted backup storage
- Host key verification for SSH connections
- Least privilege deployment user
- Automatic security updates

## [1.0.0] - 2025-01-XX

### Added
- Complete deployment automation
- Production-ready security configuration
- Backup and restore system
- Monitoring integration
- Documentation suite

### Changed
- N/A (initial release)

### Deprecated
- N/A (initial release)

### Removed
- N/A (initial release)

### Fixed
- N/A (initial release)

### Security
- Implemented comprehensive security measures
- Added encrypted backup system
- Configured proper firewall rules

---

## Release Notes

### Version 1.0.0

This is the initial release of the n8n Self-Host Installer, providing a complete solution for deploying n8n in a production environment.

#### Key Features
- **One-Click Deployment**: Deploy n8n with a single GitHub Actions workflow
- **Production Security**: HTTPS with Let's Encrypt, firewall configuration, and secure defaults
- **Automated Backups**: Daily backups to Google Drive with 30-day retention
- **Easy Restoration**: One-click backup restoration via GitHub Actions
- **Optional Monitoring**: Beszel system monitoring and Uptime-Kuma uptime monitoring
- **Sample Workflows**: Three ready-to-use n8n workflows for common use cases

#### System Requirements
- Ubuntu 22.04 LTS server
- Minimum 2GB RAM, 20GB disk space
- SSH access with key authentication
- Domain name with DNS configured

#### Security Features
- SSH key authentication (no passwords)
- UFW firewall with minimal open ports
- Let's Encrypt SSL certificates
- Encrypted backup storage
- Regular security updates
- Least privilege deployment user

#### Getting Started
1. Fork this repository
2. Configure GitHub Secrets
3. Run the Combined Setup workflow
4. Access your n8n instance via HTTPS

For detailed instructions, see [QUICKSTART.md](QUICKSTART.md).

#### Known Limitations
- Single-server deployment only
- Requires manual DNS configuration
- Google Drive required for backups
- Ubuntu 22.04 LTS only

#### Support
- Documentation: README.md, QUICKSTART.md, SECURITY.md
- Issues: GitHub Issues
- Community: GitHub Discussions
- Security: <EMAIL>

---

## Development Notes

### Versioning Strategy
- **Major versions**: Breaking changes, new architecture
- **Minor versions**: New features, backward compatible
- **Patch versions**: Bug fixes, security updates

### Release Process
1. Update CHANGELOG.md
2. Update version in scripts and documentation
3. Create release branch
4. Run full test suite
5. Security review
6. Create GitHub release
7. Update documentation

### Contribution Guidelines
See [CONTRIBUTING.md](CONTRIBUTING.md) for detailed contribution guidelines.

---

## Migration Notes

### From Manual Installation
If you have an existing manual n8n installation:

1. **Backup your data** before migration
2. **Export workflows** from your current n8n instance
3. **Note custom configurations** and environment variables
4. **Deploy using this installer** on a new server
5. **Import workflows** to the new instance
6. **Update DNS** to point to the new server
7. **Verify functionality** before decommissioning old server

### From Docker Compose
If you're using a custom Docker Compose setup:

1. **Review the included docker-compose.yml** for compatibility
2. **Migrate environment variables** to the new format
3. **Backup existing data** before switching
4. **Test the new deployment** in parallel
5. **Migrate data** using backup/restore functionality

---

## Security Updates

### Security Patch Policy
- **Critical vulnerabilities**: Patched within 24-48 hours
- **High severity**: Patched within 1 week
- **Medium/Low severity**: Included in next regular release

### Security Notifications
- GitHub Security Advisories for critical issues
- Release notes for all security updates
- Email notifications for subscribers (if available)

### Reporting Security Issues
See [SECURITY.md](SECURITY.md) for security reporting guidelines.

---

## Acknowledgments

### Contributors
- Initial development team
- Community contributors
- Security researchers
- Documentation contributors

### Third-Party Software
- [n8n](https://n8n.io/) - Workflow automation platform
- [Docker](https://docker.com/) - Containerization
- [NGINX](https://nginx.org/) - Reverse proxy
- [Let's Encrypt](https://letsencrypt.org/) - SSL certificates
- [rclone](https://rclone.org/) - Cloud storage sync
- [PostgreSQL](https://postgresql.org/) - Database
- [Redis](https://redis.io/) - In-memory data store

### Special Thanks
- n8n community for inspiration and feedback
- Open source contributors for tools and libraries
- Security researchers for responsible disclosure
- Beta testers for early feedback

---

**Note**: This changelog follows the [Keep a Changelog](https://keepachangelog.com/) format. For the complete history, see the [GitHub releases](../../releases) page.
