name: Backup n8n Data

on:
  workflow_dispatch:
    inputs:
      server_host:
        description: 'Server hostname or IP address'
        required: false
        type: string
      server_user:
        description: 'SSH username'
        required: false
        type: string
        default: 'deployer'
      backup_type:
        description: 'Backup type'
        required: false
        type: choice
        options:
          - 'full'
          - 'data-only'
          - 'config-only'
        default: 'full'
  schedule:
    # Run daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_call:
    inputs:
      server_host:
        description: 'Server hostname or IP address'
        required: false
        type: string
      server_user:
        description: 'SSH username'
        required: false
        type: string
        default: 'deployer'
      backup_type:
        description: 'Backup type'
        required: false
        type: string
        default: 'full'

env:
  SERVER_HOST: ${{ inputs.server_host || secrets.SERVER_HOST }}
  SERVER_USER: ${{ inputs.server_user || secrets.SERVER_USER || 'deployer' }}
  BACKUP_TYPE: ${{ inputs.backup_type || 'full' }}

jobs:
  backup:
    name: Create n8n Backup
    runs-on: ubuntu-latest
    timeout-minutes: 30
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Validate inputs
        run: |
          if [ -z "$SERVER_HOST" ]; then
            echo "❌ SERVER_HOST is required"
            exit 1
          fi
          
          if [ -z "$SERVER_USER" ]; then
            echo "❌ SERVER_USER is required"
            exit 1
          fi
          
          echo "✅ Backup target: $SERVER_USER@$SERVER_HOST"
          echo "✅ Backup type: $BACKUP_TYPE"
          
      - name: Setup SSH agent
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.SSH_DEPLOY_KEY }}
          
      - name: Add SSH known hosts
        run: |
          if [ -n "${{ secrets.SSH_KNOWN_HOSTS }}" ]; then
            echo "${{ secrets.SSH_KNOWN_HOSTS }}" >> ~/.ssh/known_hosts
          else
            echo "Host *" >> ~/.ssh/config
            echo "  StrictHostKeyChecking no" >> ~/.ssh/config
            echo "  UserKnownHostsFile /dev/null" >> ~/.ssh/config
          fi
          
      - name: Test SSH connection
        run: |
          echo "🔍 Testing SSH connection..."
          ssh -o ConnectTimeout=10 $SERVER_USER@$SERVER_HOST "echo 'SSH connection successful'"
          
      - name: Check backup prerequisites
        run: |
          echo "🔍 Checking backup prerequisites..."
          ssh $SERVER_USER@$SERVER_HOST '
            # Check if n8n is deployed
            if ! [ -f "/opt/n8n/docker-compose.yml" ]; then
              echo "❌ n8n is not deployed"
              exit 1
            fi
            
            # Check if rclone is configured
            if ! command -v rclone >/dev/null 2>&1; then
              echo "❌ rclone is not installed"
              exit 1
            fi
            
            # Check rclone configuration
            if ! rclone listremotes | grep -q "gdrive:"; then
              echo "❌ rclone Google Drive remote not configured"
              echo "Please run: rclone config"
              exit 1
            fi
            
            # Test rclone connection
            if ! rclone lsd gdrive: >/dev/null 2>&1; then
              echo "❌ Cannot connect to Google Drive"
              echo "Please check rclone configuration"
              exit 1
            fi
            
            # Check if n8n is running
            cd /opt/n8n
            if ! docker compose ps n8n | grep -q "running"; then
              echo "⚠️ n8n is not running, backup may be incomplete"
            fi
            
            echo "✅ Backup prerequisites check passed"
          '
          
      - name: Copy backup script
        run: |
          echo "📤 Copying backup script..."
          scp scripts/backup_n8n.sh $SERVER_USER@$SERVER_HOST:/tmp/backup_n8n.sh
          ssh $SERVER_USER@$SERVER_HOST "chmod +x /tmp/backup_n8n.sh"
          
      - name: Create backup
        id: backup
        run: |
          echo "🗄️ Creating backup..."
          
          BACKUP_OUTPUT=$(ssh $SERVER_USER@$SERVER_HOST '
            cd /opt/n8n
            
            # Set backup type environment variable
            export BACKUP_TYPE="'$BACKUP_TYPE'"
            
            # Run backup script
            if /tmp/backup_n8n.sh; then
              echo "✅ Backup created successfully"
              
              # Get backup information
              LATEST_BACKUP=$(ls -t /var/backups/n8n/ | head -1 2>/dev/null || echo "")
              if [ -n "$LATEST_BACKUP" ]; then
                echo "backup_name=$LATEST_BACKUP"
              fi
              
              # Get backup size
              BACKUP_SIZE=$(rclone size gdrive:n8n-backups --json 2>/dev/null | jq -r ".bytes" || echo "unknown")
              echo "backup_size=$BACKUP_SIZE"
              
            else
              echo "❌ Backup failed"
              exit 1
            fi
            
            # Cleanup
            rm -f /tmp/backup_n8n.sh
          ')
          
          echo "$BACKUP_OUTPUT"
          
          # Extract backup information
          BACKUP_NAME=$(echo "$BACKUP_OUTPUT" | grep "backup_name=" | cut -d'=' -f2)
          BACKUP_SIZE=$(echo "$BACKUP_OUTPUT" | grep "backup_size=" | cut -d'=' -f2)
          
          echo "backup_name=$BACKUP_NAME" >> $GITHUB_OUTPUT
          echo "backup_size=$BACKUP_SIZE" >> $GITHUB_OUTPUT
          
      - name: Verify backup
        run: |
          echo "🔍 Verifying backup..."
          ssh $SERVER_USER@$SERVER_HOST '
            # List recent backups
            echo "=== Recent Backups ==="
            rclone ls gdrive:n8n-backups | tail -10
            echo ""
            
            # Check backup integrity
            echo "=== Backup Verification ==="
            LATEST_BACKUP=$(rclone lsf gdrive:n8n-backups --max-age 1h | head -1)
            
            if [ -n "$LATEST_BACKUP" ]; then
              echo "✅ Latest backup found: $LATEST_BACKUP"
              
              # Get backup size
              BACKUP_SIZE=$(rclone size "gdrive:n8n-backups/$LATEST_BACKUP" --json | jq -r ".bytes")
              BACKUP_SIZE_MB=$((BACKUP_SIZE / 1024 / 1024))
              echo "✅ Backup size: ${BACKUP_SIZE_MB}MB"
              
              # Basic integrity check
              if [ "$BACKUP_SIZE" -gt 1000 ]; then
                echo "✅ Backup size looks reasonable"
              else
                echo "⚠️ Backup size seems small, please verify manually"
              fi
              
            else
              echo "❌ No recent backup found"
              exit 1
            fi
          '
          
      - name: Check backup retention
        run: |
          echo "🧹 Checking backup retention..."
          ssh $SERVER_USER@$SERVER_HOST '
            # Count total backups
            TOTAL_BACKUPS=$(rclone lsf gdrive:n8n-backups | wc -l)
            echo "Total backups: $TOTAL_BACKUPS"
            
            # Count backups older than retention period
            OLD_BACKUPS=$(rclone lsf gdrive:n8n-backups --max-age 30d | wc -l)
            RETENTION_BACKUPS=$((TOTAL_BACKUPS - OLD_BACKUPS))
            
            echo "Backups within retention (30 days): $OLD_BACKUPS"
            echo "Backups beyond retention: $RETENTION_BACKUPS"
            
            if [ "$RETENTION_BACKUPS" -gt 0 ]; then
              echo "✅ Old backups will be cleaned up automatically"
            fi
            
            # Show backup storage usage
            TOTAL_SIZE=$(rclone size gdrive:n8n-backups --json | jq -r ".bytes")
            TOTAL_SIZE_GB=$((TOTAL_SIZE / 1024 / 1024 / 1024))
            echo "Total backup storage: ${TOTAL_SIZE_GB}GB"
          '
          
      - name: Test backup download
        if: github.event_name == 'workflow_dispatch'
        run: |
          echo "🧪 Testing backup download (workflow_dispatch only)..."
          ssh $SERVER_USER@$SERVER_HOST '
            # Download latest backup to test
            LATEST_BACKUP=$(rclone lsf gdrive:n8n-backups --max-age 1h | head -1)
            
            if [ -n "$LATEST_BACKUP" ]; then
              echo "Testing download of: $LATEST_BACKUP"
              
              # Create test directory
              mkdir -p /tmp/backup-test
              
              # Download backup
              if rclone copy "gdrive:n8n-backups/$LATEST_BACKUP" /tmp/backup-test/; then
                echo "✅ Backup download successful"
                
                # Test extraction
                cd /tmp/backup-test
                if tar -tzf "$LATEST_BACKUP" >/dev/null 2>&1; then
                  echo "✅ Backup archive is valid"
                else
                  echo "❌ Backup archive is corrupted"
                  exit 1
                fi
                
                # Cleanup
                rm -rf /tmp/backup-test
                
              else
                echo "❌ Backup download failed"
                exit 1
              fi
            else
              echo "❌ No recent backup to test"
              exit 1
            fi
          '
          
      - name: Send notification
        if: always()
        run: |
          # Determine status
          if [ "${{ job.status }}" = "success" ]; then
            STATUS="✅ SUCCESS"
            COLOR="good"
          else
            STATUS="❌ FAILED"
            COLOR="danger"
          fi
          
          # Prepare notification message
          MESSAGE="n8n Backup $STATUS
          
          **Server:** $SERVER_USER@$SERVER_HOST
          **Type:** $BACKUP_TYPE
          **Time:** $(date -Iseconds)
          **Workflow:** ${{ github.workflow }}
          **Run ID:** ${{ github.run_id }}"
          
          if [ "${{ steps.backup.outputs.backup_name }}" != "" ]; then
            MESSAGE="$MESSAGE
          **Backup:** ${{ steps.backup.outputs.backup_name }}"
          fi
          
          if [ "${{ steps.backup.outputs.backup_size }}" != "" ] && [ "${{ steps.backup.outputs.backup_size }}" != "unknown" ]; then
            BACKUP_SIZE_MB=$((${{ steps.backup.outputs.backup_size }} / 1024 / 1024))
            MESSAGE="$MESSAGE
          **Size:** ${BACKUP_SIZE_MB}MB"
          fi
          
          echo "Backup completed with status: $STATUS"
          echo "Message: $MESSAGE"
          
          # Here you could add webhook notifications to Slack, Discord, etc.
          # Example for webhook notification:
          # curl -X POST -H 'Content-type: application/json' \
          #   --data "{\"text\":\"$MESSAGE\"}" \
          #   "${{ secrets.WEBHOOK_URL }}"
          
      - name: Display backup summary
        if: always()
        run: |
          echo "## 🗄️ Backup Summary"
          echo ""
          echo "**Status:** ${{ job.status == 'success' && '✅ Success' || '❌ Failed' }}"
          echo "**Server:** $SERVER_USER@$SERVER_HOST"
          echo "**Type:** $BACKUP_TYPE"
          echo "**Timestamp:** $(date -Iseconds)"
          echo ""
          
          if [ "${{ steps.backup.outputs.backup_name }}" != "" ]; then
            echo "**Backup Name:** ${{ steps.backup.outputs.backup_name }}"
          fi
          
          if [ "${{ steps.backup.outputs.backup_size }}" != "" ] && [ "${{ steps.backup.outputs.backup_size }}" != "unknown" ]; then
            BACKUP_SIZE_MB=$((${{ steps.backup.outputs.backup_size }} / 1024 / 1024))
            echo "**Backup Size:** ${BACKUP_SIZE_MB}MB"
          fi
          
          echo ""
          echo "### Next Steps:"
          echo "- Backups are stored in Google Drive: gdrive:n8n-backups"
          echo "- Retention period: 30 days (configurable)"
          echo "- To restore: Run the 'Restore n8n Data' workflow"
          echo "- To list backups: \`ssh $SERVER_USER@$SERVER_HOST 'rclone ls gdrive:n8n-backups'\`"
          
          if [ "${{ job.status }}" != "success" ]; then
            echo ""
            echo "### Troubleshooting:"
            echo "- Check rclone configuration: \`ssh $SERVER_USER@$SERVER_HOST 'rclone config'\`"
            echo "- Verify Google Drive permissions"
            echo "- Check disk space on server"
            echo "- Review backup logs: \`ssh $SERVER_USER@$SERVER_HOST 'tail -f /var/log/n8n/backup.log'\`"
          fi
