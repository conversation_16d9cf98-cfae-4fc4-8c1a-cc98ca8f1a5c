name: Combined Setup

on:
  workflow_dispatch:
    inputs:
      server_host:
        description: 'Server hostname or IP address'
        required: true
        type: string
      server_user:
        description: 'SSH username'
        required: false
        type: string
        default: 'deployer'
      domain_name:
        description: 'Domain name for SSL certificate'
        required: true
        type: string
      email_for_letsencrypt:
        description: 'Email for Let\'s Encrypt notifications'
        required: true
        type: string
      install_monitoring:
        description: 'Install monitoring (Uptime-Kuma)'
        required: false
        type: boolean
        default: false
      n8n_version:
        description: 'n8n Docker image tag'
        required: false
        type: string
        default: 'latest'
      skip_vm_setup:
        description: 'Skip VM setup (if already configured)'
        required: false
        type: boolean
        default: false

env:
  SERVER_HOST: ${{ inputs.server_host }}
  SERVER_USER: ${{ inputs.server_user || 'deployer' }}
  DOMAIN_NAME: ${{ inputs.domain_name }}
  EMAIL_FOR_LETSENCRYPT: ${{ inputs.email_for_letsencrypt }}
  N8N_VERSION: ${{ inputs.n8n_version || 'latest' }}

jobs:
  validate-inputs:
    name: Validate Inputs
    runs-on: ubuntu-latest
    timeout-minutes: 5
    
    steps:
      - name: Validate required inputs
        run: |
          echo "🔍 Validating inputs..."
          
          # Check required inputs
          if [ -z "${{ inputs.server_host }}" ]; then
            echo "❌ Server host is required"
            exit 1
          fi
          
          if [ -z "${{ inputs.domain_name }}" ]; then
            echo "❌ Domain name is required"
            exit 1
          fi
          
          if [ -z "${{ inputs.email_for_letsencrypt }}" ]; then
            echo "❌ Email for Let's Encrypt is required"
            exit 1
          fi
          
          # Validate domain format
          if [[ ! "${{ inputs.domain_name }}" =~ ^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$ ]]; then
            echo "❌ Invalid domain name format: ${{ inputs.domain_name }}"
            exit 1
          fi
          
          # Validate email format
          if [[ ! "${{ inputs.email_for_letsencrypt }}" =~ ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$ ]]; then
            echo "❌ Invalid email format: ${{ inputs.email_for_letsencrypt }}"
            exit 1
          fi
          
          echo "✅ Input validation passed"
          echo "Server: ${{ inputs.server_user }}@${{ inputs.server_host }}"
          echo "Domain: ${{ inputs.domain_name }}"
          echo "Email: ${{ inputs.email_for_letsencrypt }}"
          echo "Monitoring: ${{ inputs.install_monitoring }}"
          echo "n8n Version: ${{ inputs.n8n_version }}"
          
      - name: Check DNS resolution
        run: |
          echo "🔍 Checking DNS resolution..."
          
          # Check if domain resolves
          DOMAIN_IP=$(dig +short "${{ inputs.domain_name }}" | tail -n1)
          
          if [ -z "$DOMAIN_IP" ]; then
            echo "⚠️ Domain ${{ inputs.domain_name }} does not resolve"
            echo "Please ensure your domain points to ${{ inputs.server_host }}"
            echo "This may cause SSL certificate generation to fail"
          else
            echo "✅ Domain resolves to: $DOMAIN_IP"
            
            if [ "$DOMAIN_IP" != "${{ inputs.server_host }}" ]; then
              echo "⚠️ Domain resolves to $DOMAIN_IP but server is ${{ inputs.server_host }}"
              echo "This may cause SSL certificate generation to fail"
            else
              echo "✅ DNS configuration looks correct"
            fi
          fi

  setup-vm:
    name: Setup Virtual Machine
    runs-on: ubuntu-latest
    needs: validate-inputs
    if: ${{ !inputs.skip_vm_setup }}
    
    steps:
      - name: Call setup-vm workflow
        uses: ./.github/workflows/setup-vm.yml
        with:
          server_host: ${{ inputs.server_host }}
          server_user: ${{ inputs.server_user }}
          force_setup: false

  deploy-n8n:
    name: Deploy n8n
    runs-on: ubuntu-latest
    needs: [validate-inputs, setup-vm]
    if: always() && (needs.setup-vm.result == 'success' || inputs.skip_vm_setup)
    
    steps:
      - name: Call deploy-n8n workflow
        uses: ./.github/workflows/deploy-n8n.yml
        with:
          server_host: ${{ inputs.server_host }}
          server_user: ${{ inputs.server_user }}
          n8n_version: ${{ inputs.n8n_version }}
          force_recreate: false

  configure-environment:
    name: Configure Environment
    runs-on: ubuntu-latest
    needs: [validate-inputs, deploy-n8n]
    if: always() && needs.deploy-n8n.result == 'success'
    timeout-minutes: 10
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Setup SSH agent
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.SSH_DEPLOY_KEY }}
          
      - name: Add SSH known hosts
        run: |
          if [ -n "${{ secrets.SSH_KNOWN_HOSTS }}" ]; then
            echo "${{ secrets.SSH_KNOWN_HOSTS }}" >> ~/.ssh/known_hosts
          else
            echo "Host *" >> ~/.ssh/config
            echo "  StrictHostKeyChecking no" >> ~/.ssh/config
            echo "  UserKnownHostsFile /dev/null" >> ~/.ssh/config
          fi
          
      - name: Update environment configuration
        run: |
          echo "🔧 Updating environment configuration..."
          ssh $SERVER_USER@$SERVER_HOST '
            cd /opt/n8n
            
            # Update domain name in environment
            if grep -q "^DOMAIN_NAME=" .env; then
              sed -i "s/^DOMAIN_NAME=.*/DOMAIN_NAME=${{ inputs.domain_name }}/" .env
            else
              echo "DOMAIN_NAME=${{ inputs.domain_name }}" >> .env
            fi
            
            # Update email for Let'\''s Encrypt
            if grep -q "^EMAIL_FOR_LETSENCRYPT=" .env; then
              sed -i "s/^EMAIL_FOR_LETSENCRYPT=.*/EMAIL_FOR_LETSENCRYPT=${{ inputs.email_for_letsencrypt }}/" .env
            else
              echo "EMAIL_FOR_LETSENCRYPT=${{ inputs.email_for_letsencrypt }}" >> .env
            fi
            
            # Enable monitoring if requested
            if [ "${{ inputs.install_monitoring }}" = "true" ]; then
              if grep -q "^UPTIME_KUMA_ENABLED=" .env; then
                sed -i "s/^UPTIME_KUMA_ENABLED=.*/UPTIME_KUMA_ENABLED=true/" .env
              else
                echo "UPTIME_KUMA_ENABLED=true" >> .env
              fi
            fi
            
            echo "✅ Environment configuration updated"
          '

  configure-proxy:
    name: Configure Reverse Proxy
    runs-on: ubuntu-latest
    needs: [validate-inputs, configure-environment]
    if: always() && needs.configure-environment.result == 'success'
    timeout-minutes: 15
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Setup SSH agent
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.SSH_DEPLOY_KEY }}
          
      - name: Add SSH known hosts
        run: |
          if [ -n "${{ secrets.SSH_KNOWN_HOSTS }}" ]; then
            echo "${{ secrets.SSH_KNOWN_HOSTS }}" >> ~/.ssh/known_hosts
          else
            echo "Host *" >> ~/.ssh/config
            echo "  StrictHostKeyChecking no" >> ~/.ssh/config
            echo "  UserKnownHostsFile /dev/null" >> ~/.ssh/config
          fi
          
      - name: Configure NGINX and SSL
        run: |
          echo "🔧 Configuring reverse proxy..."
          
          # Copy configure script
          scp scripts/configure_proxy.sh $SERVER_USER@$SERVER_HOST:/tmp/configure_proxy.sh
          ssh $SERVER_USER@$SERVER_HOST "chmod +x /tmp/configure_proxy.sh"
          
          # Run configuration
          ssh $SERVER_USER@$SERVER_HOST '
            if /tmp/configure_proxy.sh --domain "${{ inputs.domain_name }}" --email "${{ inputs.email_for_letsencrypt }}"; then
              echo "✅ Reverse proxy configured successfully"
            else
              echo "❌ Reverse proxy configuration failed"
              exit 1
            fi
            
            # Cleanup
            rm -f /tmp/configure_proxy.sh
          '

  setup-monitoring:
    name: Setup Monitoring
    runs-on: ubuntu-latest
    needs: [validate-inputs, configure-proxy]
    if: always() && needs.configure-proxy.result == 'success' && inputs.install_monitoring
    timeout-minutes: 10
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Setup SSH agent
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.SSH_DEPLOY_KEY }}
          
      - name: Add SSH known hosts
        run: |
          if [ -n "${{ secrets.SSH_KNOWN_HOSTS }}" ]; then
            echo "${{ secrets.SSH_KNOWN_HOSTS }}" >> ~/.ssh/known_hosts
          else
            echo "Host *" >> ~/.ssh/config
            echo "  StrictHostKeyChecking no" >> ~/.ssh/config
            echo "  UserKnownHostsFile /dev/null" >> ~/.ssh/config
          fi
          
      - name: Deploy Uptime-Kuma
        run: |
          echo "📊 Setting up monitoring..."
          
          # Copy monitoring script
          scp scripts/deploy_uptime_kuma.sh $SERVER_USER@$SERVER_HOST:/tmp/deploy_uptime_kuma.sh
          ssh $SERVER_USER@$SERVER_HOST "chmod +x /tmp/deploy_uptime_kuma.sh"
          
          # Deploy monitoring
          ssh $SERVER_USER@$SERVER_HOST '
            if /tmp/deploy_uptime_kuma.sh; then
              echo "✅ Monitoring setup completed"
            else
              echo "❌ Monitoring setup failed"
              # Don'\''t fail the entire workflow for monitoring
            fi
            
            # Cleanup
            rm -f /tmp/deploy_uptime_kuma.sh
          '

  setup-backups:
    name: Setup Backups
    runs-on: ubuntu-latest
    needs: [validate-inputs, configure-proxy]
    if: always() && needs.configure-proxy.result == 'success'
    timeout-minutes: 5
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Setup SSH agent
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.SSH_DEPLOY_KEY }}
          
      - name: Add SSH known hosts
        run: |
          if [ -n "${{ secrets.SSH_KNOWN_HOSTS }}" ]; then
            echo "${{ secrets.SSH_KNOWN_HOSTS }}" >> ~/.ssh/known_hosts
          else
            echo "Host *" >> ~/.ssh/config
            echo "  StrictHostKeyChecking no" >> ~/.ssh/config
            echo "  UserKnownHostsFile /dev/null" >> ~/.ssh/config
          fi
          
      - name: Test backup configuration
        run: |
          echo "🗄️ Testing backup configuration..."
          
          # Copy backup script
          scp scripts/backup_n8n.sh $SERVER_USER@$SERVER_HOST:/tmp/backup_n8n.sh
          ssh $SERVER_USER@$SERVER_HOST "chmod +x /tmp/backup_n8n.sh"
          
          # Test backup configuration
          ssh $SERVER_USER@$SERVER_HOST '
            if /tmp/backup_n8n.sh --test; then
              echo "✅ Backup configuration test passed"
            else
              echo "⚠️ Backup configuration test failed"
              echo "Please configure rclone manually:"
              echo "ssh $USER@$(hostname) rclone config"
            fi
            
            # Cleanup
            rm -f /tmp/backup_n8n.sh
          '

  final-verification:
    name: Final Verification
    runs-on: ubuntu-latest
    needs: [setup-vm, deploy-n8n, configure-proxy, setup-monitoring, setup-backups]
    if: always()
    timeout-minutes: 10
    
    steps:
      - name: Setup SSH agent
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.SSH_DEPLOY_KEY }}
          
      - name: Add SSH known hosts
        run: |
          if [ -n "${{ secrets.SSH_KNOWN_HOSTS }}" ]; then
            echo "${{ secrets.SSH_KNOWN_HOSTS }}" >> ~/.ssh/known_hosts
          else
            echo "Host *" >> ~/.ssh/config
            echo "  StrictHostKeyChecking no" >> ~/.ssh/config
            echo "  UserKnownHostsFile /dev/null" >> ~/.ssh/config
          fi
          
      - name: Verify complete installation
        run: |
          echo "🔍 Performing final verification..."
          ssh $SERVER_USER@$SERVER_HOST '
            echo "=== Final Installation Verification ==="
            echo ""
            
            # Check n8n accessibility
            if curl -f -s https://${{ inputs.domain_name }}/healthz >/dev/null 2>&1; then
              echo "✅ n8n HTTPS: Accessible at https://${{ inputs.domain_name }}"
            elif curl -f -s http://localhost:5678/healthz >/dev/null 2>&1; then
              echo "⚠️ n8n HTTP: Accessible at http://localhost:5678 (HTTPS may need time to propagate)"
            else
              echo "❌ n8n: Not accessible"
            fi
            
            # Check SSL certificate
            if openssl s_client -servername ${{ inputs.domain_name }} -connect ${{ inputs.domain_name }}:443 </dev/null 2>/dev/null | grep -q "Verify return code: 0"; then
              echo "✅ SSL Certificate: Valid"
            else
              echo "⚠️ SSL Certificate: May need time to propagate"
            fi
            
            # Check services
            cd /opt/n8n
            echo ""
            echo "=== Service Status ==="
            docker compose ps
            
            echo ""
            echo "=== Installation Summary ==="
            echo "Server: ${{ inputs.server_user }}@${{ inputs.server_host }}"
            echo "Domain: ${{ inputs.domain_name }}"
            echo "n8n Version: ${{ inputs.n8n_version }}"
            echo "Monitoring: ${{ inputs.install_monitoring }}"
            echo "Timestamp: $(date -Iseconds)"
          '
          
      - name: Display completion summary
        if: always()
        run: |
          echo "## 🎉 n8n Installation Complete!"
          echo ""
          
          # Determine overall status
          OVERALL_STATUS="success"
          if [ "${{ needs.setup-vm.result }}" = "failure" ] || [ "${{ needs.deploy-n8n.result }}" = "failure" ] || [ "${{ needs.configure-proxy.result }}" = "failure" ]; then
            OVERALL_STATUS="partial"
          fi
          
          if [ "$OVERALL_STATUS" = "success" ]; then
            echo "### ✅ Installation Successful"
            echo ""
            echo "Your n8n instance is ready to use!"
            echo ""
            echo "**Access Information:**"
            echo "- **n8n Web Interface:** https://${{ inputs.domain_name }}"
            echo "- **Server SSH:** \`ssh ${{ inputs.server_user }}@${{ inputs.server_host }}\`"
            
            if [ "${{ inputs.install_monitoring }}" = "true" ]; then
              echo "- **Monitoring:** https://uptime.${{ inputs.domain_name }}"
            fi
            
            echo ""
            echo "**Next Steps:**"
            echo "1. Open https://${{ inputs.domain_name }} in your browser"
            echo "2. Create your admin account"
            echo "3. Import sample workflows from the \`workflows/\` directory"
            echo "4. Configure rclone for backups: \`ssh ${{ inputs.server_user }}@${{ inputs.server_host }} rclone config\`"
            echo "5. Test the backup system"
            
          else
            echo "### ⚠️ Installation Partially Complete"
            echo ""
            echo "Some components may need manual configuration."
            echo ""
            echo "**Check the following:**"
            echo "- DNS: Ensure ${{ inputs.domain_name }} points to ${{ inputs.server_host }}"
            echo "- SSL: Certificate generation may take a few minutes"
            echo "- Backups: Configure rclone manually if needed"
          fi
          
          echo ""
          echo "**Support:**"
          echo "- Documentation: README.md, QUICKSTART.md"
          echo "- Issues: GitHub Issues"
          echo "- Logs: \`ssh ${{ inputs.server_user }}@${{ inputs.server_host }} 'tail -f /var/log/n8n/*.log'\`"
