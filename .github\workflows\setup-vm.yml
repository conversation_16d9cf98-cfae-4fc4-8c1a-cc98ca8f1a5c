name: Setup VM

on:
  workflow_dispatch:
    inputs:
      server_host:
        description: 'Server hostname or IP address'
        required: false
        type: string
      server_user:
        description: 'SSH username'
        required: false
        type: string
        default: 'deployer'
      force_setup:
        description: 'Force setup even if already configured'
        required: false
        type: boolean
        default: false
  workflow_call:
    inputs:
      server_host:
        description: 'Server hostname or IP address'
        required: false
        type: string
      server_user:
        description: 'SSH username'
        required: false
        type: string
        default: 'deployer'
      force_setup:
        description: 'Force setup even if already configured'
        required: false
        type: boolean
        default: false

env:
  SERVER_HOST: ${{ inputs.server_host || secrets.SERVER_HOST }}
  SERVER_USER: ${{ inputs.server_user || secrets.SERVER_USER || 'deployer' }}

jobs:
  setup-vm:
    name: Setup Virtual Machine
    runs-on: ubuntu-latest
    timeout-minutes: 30
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Validate inputs
        run: |
          if [ -z "$SERVER_HOST" ]; then
            echo "❌ SERVER_HOST is required"
            echo "Set it as a secret or provide as input"
            exit 1
          fi
          
          if [ -z "$SERVER_USER" ]; then
            echo "❌ SERVER_USER is required"
            echo "Set it as a secret or provide as input"
            exit 1
          fi
          
          echo "✅ Server: $SERVER_USER@$SERVER_HOST"
          
      - name: Setup SSH agent
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.SSH_DEPLOY_KEY }}
          
      - name: Add SSH known hosts
        run: |
          if [ -n "${{ secrets.SSH_KNOWN_HOSTS }}" ]; then
            echo "${{ secrets.SSH_KNOWN_HOSTS }}" >> ~/.ssh/known_hosts
            echo "✅ SSH known hosts added"
          else
            echo "⚠️ SSH_KNOWN_HOSTS not set, using StrictHostKeyChecking=no"
            echo "Host *" >> ~/.ssh/config
            echo "  StrictHostKeyChecking no" >> ~/.ssh/config
            echo "  UserKnownHostsFile /dev/null" >> ~/.ssh/config
          fi
          
      - name: Test SSH connection
        run: |
          echo "🔍 Testing SSH connection..."
          ssh -o ConnectTimeout=10 $SERVER_USER@$SERVER_HOST "echo 'SSH connection successful'"
          
      - name: Check system requirements
        run: |
          echo "🔍 Checking system requirements..."
          ssh $SERVER_USER@$SERVER_HOST '
            echo "=== System Information ==="
            uname -a
            echo ""
            
            echo "=== OS Version ==="
            cat /etc/os-release | grep PRETTY_NAME
            echo ""
            
            echo "=== Memory ==="
            free -h
            echo ""
            
            echo "=== Disk Space ==="
            df -h /
            echo ""
            
            echo "=== Network ==="
            ip addr show | grep "inet " | head -5
            echo ""
            
            # Check minimum requirements
            MEM_GB=$(free -g | awk "/^Mem:/{print \$2}")
            DISK_GB=$(df / | awk "NR==2{print int(\$4/1024/1024)}")
            
            echo "=== Requirements Check ==="
            if [ "$MEM_GB" -lt 1 ]; then
              echo "❌ Insufficient memory: ${MEM_GB}GB (minimum 1GB required)"
              exit 1
            else
              echo "✅ Memory: ${MEM_GB}GB"
            fi
            
            if [ "$DISK_GB" -lt 10 ]; then
              echo "❌ Insufficient disk space: ${DISK_GB}GB (minimum 10GB required)"
              exit 1
            else
              echo "✅ Disk space: ${DISK_GB}GB"
            fi
            
            # Check if Ubuntu
            if ! grep -q "Ubuntu" /etc/os-release; then
              echo "⚠️ Non-Ubuntu OS detected, setup may not work correctly"
            else
              echo "✅ Ubuntu OS detected"
            fi
          '
          
      - name: Check if already configured
        id: check_configured
        run: |
          echo "🔍 Checking if VM is already configured..."
          
          CONFIGURED="false"
          if ssh $SERVER_USER@$SERVER_HOST '
            command -v docker >/dev/null 2>&1 && 
            command -v docker-compose >/dev/null 2>&1 && 
            [ -d "/opt/n8n" ]
          '; then
            CONFIGURED="true"
            echo "✅ VM appears to be already configured"
          else
            echo "📋 VM needs configuration"
          fi
          
          echo "configured=$CONFIGURED" >> $GITHUB_OUTPUT
          
      - name: Copy setup script
        if: steps.check_configured.outputs.configured == 'false' || inputs.force_setup
        run: |
          echo "📤 Copying setup script to server..."
          scp scripts/setup_vm.sh $SERVER_USER@$SERVER_HOST:/tmp/setup_vm.sh
          ssh $SERVER_USER@$SERVER_HOST "chmod +x /tmp/setup_vm.sh"
          
      - name: Run VM setup
        if: steps.check_configured.outputs.configured == 'false' || inputs.force_setup
        run: |
          echo "🚀 Running VM setup..."
          ssh $SERVER_USER@$SERVER_HOST '
            echo "Starting VM setup at $(date)"
            
            # Run setup script
            if /tmp/setup_vm.sh; then
              echo "✅ VM setup completed successfully"
            else
              echo "❌ VM setup failed"
              exit 1
            fi
            
            # Cleanup
            rm -f /tmp/setup_vm.sh
            
            echo "Setup completed at $(date)"
          '
          
      - name: Verify installation
        run: |
          echo "🔍 Verifying installation..."
          ssh $SERVER_USER@$SERVER_HOST '
            echo "=== Verification Results ==="
            
            # Check Docker
            if command -v docker >/dev/null 2>&1; then
              echo "✅ Docker: $(docker --version)"
            else
              echo "❌ Docker not installed"
              exit 1
            fi
            
            # Check Docker Compose
            if docker compose version >/dev/null 2>&1; then
              echo "✅ Docker Compose: $(docker compose version --short)"
            elif command -v docker-compose >/dev/null 2>&1; then
              echo "✅ Docker Compose: $(docker-compose --version)"
            else
              echo "❌ Docker Compose not available"
              exit 1
            fi
            
            # Check rclone
            if command -v rclone >/dev/null 2>&1; then
              echo "✅ rclone: $(rclone --version | head -1)"
            else
              echo "❌ rclone not installed"
              exit 1
            fi
            
            # Check certbot
            if command -v certbot >/dev/null 2>&1; then
              echo "✅ Certbot: $(certbot --version)"
            else
              echo "❌ Certbot not installed"
              exit 1
            fi
            
            # Check directories
            if [ -d "/opt/n8n" ]; then
              echo "✅ n8n directory: /opt/n8n"
            else
              echo "❌ n8n directory not created"
              exit 1
            fi
            
            if [ -d "/var/backups/n8n" ]; then
              echo "✅ Backup directory: /var/backups/n8n"
            else
              echo "❌ Backup directory not created"
              exit 1
            fi
            
            # Check firewall
            if command -v ufw >/dev/null 2>&1; then
              echo "✅ UFW firewall: $(ufw status | head -1)"
            else
              echo "⚠️ UFW not available"
            fi
            
            # Check Docker service
            if systemctl is-active --quiet docker; then
              echo "✅ Docker service: running"
            else
              echo "❌ Docker service not running"
              exit 1
            fi
            
            # Check user groups
            if groups $USER | grep -q docker; then
              echo "✅ User in docker group"
            else
              echo "⚠️ User not in docker group (may need re-login)"
            fi
            
            echo ""
            echo "🎉 VM setup verification completed successfully!"
          '
          
      - name: Display next steps
        run: |
          echo "## 🎉 VM Setup Complete!"
          echo ""
          echo "Your server is now ready for n8n deployment."
          echo ""
          echo "### Next Steps:"
          echo "1. **Configure rclone for backups:**"
          echo "   \`\`\`bash"
          echo "   ssh $SERVER_USER@$SERVER_HOST"
          echo "   rclone config"
          echo "   \`\`\`"
          echo ""
          echo "2. **Deploy n8n:**"
          echo "   - Run the 'Deploy n8n' workflow"
          echo "   - Or use the 'Combined Setup' workflow"
          echo ""
          echo "3. **Configure SSL:**"
          echo "   - Ensure your domain points to: $SERVER_HOST"
          echo "   - Run the 'Configure Proxy' step"
          echo ""
          echo "### Server Information:"
          echo "- **Host:** $SERVER_HOST"
          echo "- **User:** $SERVER_USER"
          echo "- **SSH:** \`ssh $SERVER_USER@$SERVER_HOST\`"
          echo ""
          echo "### Installed Components:"
          echo "- ✅ Docker & Docker Compose"
          echo "- ✅ rclone (for backups)"
          echo "- ✅ Certbot (for SSL)"
          echo "- ✅ UFW Firewall"
          echo "- ✅ Fail2Ban"
          echo "- ✅ Required directories"
          
      - name: Save setup info
        run: |
          echo "📝 Saving setup information..."
          
          # Create setup info file
          cat > setup-info.json <<EOF
          {
            "timestamp": "$(date -Iseconds)",
            "server_host": "$SERVER_HOST",
            "server_user": "$SERVER_USER",
            "setup_completed": true,
            "workflow_run": "${{ github.run_id }}",
            "repository": "${{ github.repository }}",
            "commit": "${{ github.sha }}"
          }
          EOF
          
          # Upload to server
          scp setup-info.json $SERVER_USER@$SERVER_HOST:/opt/n8n/setup-info.json
          
          echo "✅ Setup information saved to server"
