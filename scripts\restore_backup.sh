#!/bin/bash

# n8n Self-Host Installer - <PERSON><PERSON>
# This script restores n8n data from Google Drive backups
# WARNING: This will overwrite existing n8n data!

set -euo pipefail

# =============================================================================
# CONFIGURATION
# =============================================================================

SCRIPT_NAME="restore_backup.sh"
LOG_FILE="/var/log/n8n/restore.log"
N8N_DIR="/opt/n8n"
BACKUP_DIR="/var/backups/n8n"
RESTORE_DIR="/tmp/n8n-restore"
COMPOSE_FILE="$N8N_DIR/docker-compose.yml"
ENV_FILE="$N8N_DIR/.env"

# Default restore settings
RCLONE_REMOTE="${RCLONE_REMOTE:-gdrive}"
BACKUP_DESTINATION="${BACKUP_DESTINATION:-n8n-backups}"
BACKUP_ID="${BACKUP_ID:-latest}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================

log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

log_info() {
    log "INFO" "$@"
    echo -e "${BLUE}[INFO]${NC} $*"
}

log_success() {
    log "SUCCESS" "$@"
    echo -e "${GREEN}[SUCCESS]${NC} $*"
}

log_warning() {
    log "WARNING" "$@"
    echo -e "${YELLOW}[WARNING]${NC} $*"
}

log_error() {
    log "ERROR" "$@"
    echo -e "${RED}[ERROR]${NC} $*" >&2
}

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

load_environment() {
    log_info "Loading environment configuration..."
    
    if [[ -f "$ENV_FILE" ]]; then
        set -a
        source "$ENV_FILE"
        set +a
        
        RCLONE_REMOTE="${RCLONE_REMOTE:-$RCLONE_REMOTE}"
        BACKUP_DESTINATION="${BACKUP_DESTINATION:-$BACKUP_DESTINATION}"
        
        log_success "Environment loaded from $ENV_FILE"
    else
        log_warning "Environment file not found: $ENV_FILE"
    fi
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if rclone is installed
    if ! command -v rclone &> /dev/null; then
        log_error "rclone is not installed"
        exit 1
    fi
    
    # Check if rclone remote is configured
    if ! rclone listremotes | grep -q "^${RCLONE_REMOTE}:$"; then
        log_error "rclone remote '$RCLONE_REMOTE' is not configured"
        exit 1
    fi
    
    # Test rclone connection
    if ! rclone lsd "${RCLONE_REMOTE}:" > /dev/null 2>&1; then
        log_error "Cannot connect to rclone remote '$RCLONE_REMOTE'"
        exit 1
    fi
    
    # Create directories
    mkdir -p "$BACKUP_DIR"
    mkdir -p "$RESTORE_DIR"
    
    log_success "Prerequisites check passed"
}

list_available_backups() {
    log_info "Listing available backups..."
    
    local backups=$(rclone lsf "${RCLONE_REMOTE}:${BACKUP_DESTINATION}/" --max-age 90d 2>/dev/null | grep "\.tar\.gz$" | sort -r)
    
    if [[ -z "$backups" ]]; then
        log_warning "No backups found in ${RCLONE_REMOTE}:${BACKUP_DESTINATION}/"
        return 1
    fi
    
    echo ""
    echo "Available backups:"
    echo "=================="
    local count=1
    echo "$backups" | while read -r backup; do
        if [[ -n "$backup" ]]; then
            local size=$(rclone size "${RCLONE_REMOTE}:${BACKUP_DESTINATION}/$backup" --json | jq -r '.bytes' 2>/dev/null || echo "unknown")
            local date=$(echo "$backup" | grep -oE '[0-9]{8}-[0-9]{6}' | sed 's/\([0-9]{4}\)\([0-9]{2}\)\([0-9]{2}\)-\([0-9]{2}\)\([0-9]{2}\)\([0-9]{2}\)/\1-\2-\3 \4:\5:\6/')
            printf "%2d. %-50s %s\n" "$count" "$backup" "$date"
            ((count++))
        fi
    done
    echo ""
    
    return 0
}

select_backup() {
    local backup_id="$1"
    
    if [[ "$backup_id" == "latest" ]]; then
        log_info "Selecting latest backup..."
        local latest_backup=$(rclone lsf "${RCLONE_REMOTE}:${BACKUP_DESTINATION}/" --max-age 90d 2>/dev/null | grep "\.tar\.gz$" | sort -r | head -1)
        
        if [[ -z "$latest_backup" ]]; then
            log_error "No backups found"
            exit 1
        fi
        
        echo "$latest_backup"
    else
        # Check if backup exists
        if rclone lsf "${RCLONE_REMOTE}:${BACKUP_DESTINATION}/" | grep -q "^${backup_id}$"; then
            echo "$backup_id"
        else
            log_error "Backup not found: $backup_id"
            log_info "Available backups:"
            list_available_backups
            exit 1
        fi
    fi
}

download_backup() {
    local backup_file="$1"
    local local_path="$RESTORE_DIR/$backup_file"
    
    log_info "Downloading backup: $backup_file"
    
    # Download backup
    if rclone copy "${RCLONE_REMOTE}:${BACKUP_DESTINATION}/$backup_file" "$RESTORE_DIR/"; then
        log_success "Backup downloaded successfully"
    else
        log_error "Failed to download backup"
        exit 1
    fi
    
    # Extract backup
    log_info "Extracting backup..."
    cd "$RESTORE_DIR"
    
    if tar -xzf "$backup_file"; then
        log_success "Backup extracted successfully"
        
        # Find extracted directory
        local extracted_dir=$(tar -tzf "$backup_file" | head -1 | cut -f1 -d"/")
        echo "$extracted_dir"
    else
        log_error "Failed to extract backup"
        exit 1
    fi
}

verify_backup_integrity() {
    local backup_dir="$1"
    local backup_path="$RESTORE_DIR/$backup_dir"
    
    log_info "Verifying backup integrity..."
    
    # Check backup metadata
    if [[ -f "$backup_path/backup-info.json" ]]; then
        local backup_info=$(cat "$backup_path/backup-info.json")
        log_info "Backup info: $(echo "$backup_info" | jq -r '.backup_name + " from " + .timestamp')"
    else
        log_warning "Backup metadata not found"
    fi
    
    # Check required files
    local required_files=("database.sql" "database.sql.gz")
    local db_backup_found=false
    
    for file in "${required_files[@]}"; do
        if [[ -f "$backup_path/$file" ]]; then
            db_backup_found=true
            log_info "Database backup found: $file"
            break
        fi
    done
    
    if [[ "$db_backup_found" == "false" ]]; then
        log_error "No database backup found in restore archive"
        exit 1
    fi
    
    log_success "Backup integrity verification passed"
}

create_pre_restore_backup() {
    log_info "Creating pre-restore backup of current data..."
    
    local pre_restore_name="pre-restore-$(date +%Y%m%d-%H%M%S)"
    local pre_restore_path="$BACKUP_DIR/$pre_restore_name"
    
    mkdir -p "$pre_restore_path"
    
    # Backup current n8n data
    if [[ -d "$N8N_DIR/data/n8n" ]] && [[ "$(ls -A $N8N_DIR/data/n8n)" ]]; then
        tar -czf "$pre_restore_path/current-n8n-data.tar.gz" -C "$N8N_DIR/data" n8n/
        log_info "Current n8n data backed up"
    fi
    
    # Backup current database
    cd "$N8N_DIR"
    if docker compose ps postgres | grep -q "running"; then
        local db_name="${DB_POSTGRESDB_DATABASE:-n8n}"
        local db_user="${DB_POSTGRESDB_USER:-n8n}"
        
        docker compose exec -T postgres pg_dump -U "$db_user" -d "$db_name" | gzip > "$pre_restore_path/current-database.sql.gz"
        log_info "Current database backed up"
    fi
    
    log_success "Pre-restore backup created: $pre_restore_path"
    echo "$pre_restore_path"
}

stop_services() {
    log_info "Stopping n8n services..."
    
    cd "$N8N_DIR"
    
    if [[ -f "$COMPOSE_FILE" ]]; then
        docker compose stop n8n nginx || true
        log_success "Services stopped"
    else
        log_warning "Docker Compose file not found, services may not be running"
    fi
}

restore_database() {
    local backup_dir="$1"
    local backup_path="$RESTORE_DIR/$backup_dir"
    
    log_info "Restoring database..."
    
    cd "$N8N_DIR"
    
    # Ensure PostgreSQL is running
    docker compose up -d postgres
    
    # Wait for PostgreSQL to be ready
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if docker compose exec postgres pg_isready -U "${DB_POSTGRESDB_USER:-n8n}" > /dev/null 2>&1; then
            break
        fi
        log_info "Waiting for PostgreSQL to be ready... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    if [[ $attempt -gt $max_attempts ]]; then
        log_error "PostgreSQL failed to start"
        exit 1
    fi
    
    # Drop and recreate database
    local db_name="${DB_POSTGRESDB_DATABASE:-n8n}"
    local db_user="${DB_POSTGRESDB_USER:-n8n}"
    
    docker compose exec -T postgres psql -U "$db_user" -c "DROP DATABASE IF EXISTS $db_name;"
    docker compose exec -T postgres psql -U "$db_user" -c "CREATE DATABASE $db_name;"
    
    # Restore database
    if [[ -f "$backup_path/database.sql.gz" ]]; then
        zcat "$backup_path/database.sql.gz" | docker compose exec -T postgres psql -U "$db_user" -d "$db_name"
    elif [[ -f "$backup_path/database.sql" ]]; then
        cat "$backup_path/database.sql" | docker compose exec -T postgres psql -U "$db_user" -d "$db_name"
    else
        log_error "No database backup file found"
        exit 1
    fi
    
    log_success "Database restored successfully"
}

restore_n8n_data() {
    local backup_dir="$1"
    local backup_path="$RESTORE_DIR/$backup_dir"
    
    log_info "Restoring n8n data..."
    
    # Remove current n8n data
    if [[ -d "$N8N_DIR/data/n8n" ]]; then
        rm -rf "$N8N_DIR/data/n8n"
        log_info "Current n8n data removed"
    fi
    
    # Restore n8n data
    if [[ -f "$backup_path/n8n-data.tar.gz" ]]; then
        tar -xzf "$backup_path/n8n-data.tar.gz" -C "$N8N_DIR/data/"
        log_success "n8n data restored from compressed archive"
    elif [[ -d "$backup_path/n8n-data" ]]; then
        cp -r "$backup_path/n8n-data" "$N8N_DIR/data/n8n"
        log_success "n8n data restored from directory"
    elif [[ -f "$backup_path/n8n-data-empty" ]]; then
        mkdir -p "$N8N_DIR/data/n8n"
        log_info "Empty n8n data directory created"
    else
        log_warning "No n8n data found in backup"
        mkdir -p "$N8N_DIR/data/n8n"
    fi
    
    # Set proper permissions
    chown -R 1000:1000 "$N8N_DIR/data/n8n" 2>/dev/null || true
    chmod -R 755 "$N8N_DIR/data/n8n" 2>/dev/null || true
}

start_services() {
    log_info "Starting n8n services..."
    
    cd "$N8N_DIR"
    
    # Start all services
    docker compose up -d
    
    # Wait for n8n to be ready
    local max_attempts=60
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f -s http://localhost:5678/healthz > /dev/null 2>&1; then
            log_success "n8n is ready"
            break
        fi
        log_info "Waiting for n8n to start... ($attempt/$max_attempts)"
        sleep 5
        ((attempt++))
    done
    
    if [[ $attempt -gt $max_attempts ]]; then
        log_error "n8n failed to start after restore"
        log_error "Check logs: docker compose logs n8n"
        exit 1
    fi
}

cleanup_restore() {
    log_info "Cleaning up restore files..."
    
    rm -rf "$RESTORE_DIR"
    log_success "Restore cleanup completed"
}

# =============================================================================
# MAIN RESTORE FUNCTION
# =============================================================================

restore_backup() {
    local backup_id="$1"
    
    log_info "Starting n8n restore process..."
    log_info "Timestamp: $(date)"
    log_info "Backup ID: $backup_id"
    log_info "Remote: $RCLONE_REMOTE:$BACKUP_DESTINATION"
    
    # Select backup to restore
    local backup_file=$(select_backup "$backup_id")
    log_info "Selected backup: $backup_file"
    
    # Download and extract backup
    local backup_dir=$(download_backup "$backup_file")
    
    # Verify backup integrity
    verify_backup_integrity "$backup_dir"
    
    # Create pre-restore backup
    local pre_restore_path=$(create_pre_restore_backup)
    
    # Perform restore
    stop_services
    restore_database "$backup_dir"
    restore_n8n_data "$backup_dir"
    start_services
    
    # Cleanup
    cleanup_restore
    
    log_success "Restore process completed successfully!"
    log_info "n8n has been restored from backup: $backup_file"
    log_info "Pre-restore backup saved to: $pre_restore_path"
    log_info "n8n is available at: http://localhost:5678"
}

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --backup-id)
            BACKUP_ID="$2"
            shift 2
            ;;
        --list)
            load_environment
            check_prerequisites
            list_available_backups
            exit 0
            ;;
        --help|-h)
            echo "Usage: $0 [options]"
            echo "Options:"
            echo "  --backup-id ID    Backup ID to restore (default: latest)"
            echo "  --list            List available backups"
            echo "  --help            Show this help message"
            echo ""
            echo "WARNING: This will overwrite existing n8n data!"
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Confirmation prompt
echo -e "${RED}WARNING: This will overwrite existing n8n data!${NC}"
echo "Backup ID: $BACKUP_ID"
echo "Remote: $RCLONE_REMOTE:$BACKUP_DESTINATION"
echo ""
read -p "Are you sure you want to continue? (yes/no): " -r
if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
    echo "Restore cancelled"
    exit 0
fi

# Create log directory if it doesn't exist
mkdir -p "$(dirname "$LOG_FILE")"
touch "$LOG_FILE"

# Load environment and check prerequisites
load_environment
check_prerequisites

# Perform restore
restore_backup "$BACKUP_ID"
