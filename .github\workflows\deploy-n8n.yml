name: Deploy n8n

on:
  workflow_dispatch:
    inputs:
      server_host:
        description: 'Server hostname or IP address'
        required: false
        type: string
      server_user:
        description: 'SSH username'
        required: false
        type: string
        default: 'deployer'
      n8n_version:
        description: 'n8n Docker image tag'
        required: false
        type: string
        default: 'latest'
      force_recreate:
        description: 'Force recreate containers'
        required: false
        type: boolean
        default: false
  workflow_call:
    inputs:
      server_host:
        description: 'Server hostname or IP address'
        required: false
        type: string
      server_user:
        description: 'SSH username'
        required: false
        type: string
        default: 'deployer'
      n8n_version:
        description: 'n8n Docker image tag'
        required: false
        type: string
        default: 'latest'
      force_recreate:
        description: 'Force recreate containers'
        required: false
        type: boolean
        default: false
  push:
    branches: [ main ]
    paths:
      - 'docker/**'
      - 'scripts/deploy_n8n.sh'

env:
  SERVER_HOST: ${{ inputs.server_host || secrets.SERVER_HOST }}
  SERVER_USER: ${{ inputs.server_user || secrets.SERVER_USER || 'deployer' }}
  N8N_VERSION: ${{ inputs.n8n_version || 'latest' }}

jobs:
  deploy:
    name: Deploy n8n Application
    runs-on: ubuntu-latest
    timeout-minutes: 20
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Validate inputs
        run: |
          if [ -z "$SERVER_HOST" ]; then
            echo "❌ SERVER_HOST is required"
            exit 1
          fi
          
          if [ -z "$SERVER_USER" ]; then
            echo "❌ SERVER_USER is required"
            exit 1
          fi
          
          echo "✅ Deploying to: $SERVER_USER@$SERVER_HOST"
          echo "✅ n8n version: $N8N_VERSION"
          
      - name: Setup SSH agent
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.SSH_DEPLOY_KEY }}
          
      - name: Add SSH known hosts
        run: |
          if [ -n "${{ secrets.SSH_KNOWN_HOSTS }}" ]; then
            echo "${{ secrets.SSH_KNOWN_HOSTS }}" >> ~/.ssh/known_hosts
          else
            echo "Host *" >> ~/.ssh/config
            echo "  StrictHostKeyChecking no" >> ~/.ssh/config
            echo "  UserKnownHostsFile /dev/null" >> ~/.ssh/config
          fi
          
      - name: Test SSH connection
        run: |
          echo "🔍 Testing SSH connection..."
          ssh -o ConnectTimeout=10 $SERVER_USER@$SERVER_HOST "echo 'SSH connection successful'"
          
      - name: Check prerequisites
        run: |
          echo "🔍 Checking deployment prerequisites..."
          ssh $SERVER_USER@$SERVER_HOST '
            # Check if VM is set up
            if ! command -v docker >/dev/null 2>&1; then
              echo "❌ Docker not installed. Please run setup-vm workflow first."
              exit 1
            fi
            
            if ! [ -d "/opt/n8n" ]; then
              echo "❌ n8n directory not found. Please run setup-vm workflow first."
              exit 1
            fi
            
            # Check Docker service
            if ! systemctl is-active --quiet docker; then
              echo "❌ Docker service not running"
              exit 1
            fi
            
            # Check environment file
            if ! [ -f "/opt/n8n/.env" ]; then
              echo "⚠️ Environment file not found at /opt/n8n/.env"
              echo "Creating from template..."
              cp /opt/n8n/.env.example /opt/n8n/.env 2>/dev/null || echo "No .env.example found"
            fi
            
            echo "✅ Prerequisites check passed"
          '
          
      - name: Copy deployment files
        run: |
          echo "📤 Copying deployment files..."
          
          # Copy Docker configuration
          scp -r docker/ $SERVER_USER@$SERVER_HOST:/tmp/docker-deploy/
          
          # Copy deployment script
          scp scripts/deploy_n8n.sh $SERVER_USER@$SERVER_HOST:/tmp/deploy_n8n.sh
          chmod +x /tmp/deploy_n8n.sh
          
          echo "✅ Files copied successfully"
          
      - name: Update Docker configuration
        run: |
          echo "🔧 Updating Docker configuration..."
          ssh $SERVER_USER@$SERVER_HOST '
            # Backup existing configuration
            if [ -f "/opt/n8n/docker-compose.yml" ]; then
              cp /opt/n8n/docker-compose.yml /opt/n8n/docker-compose.yml.backup.$(date +%Y%m%d-%H%M%S)
              echo "✅ Existing configuration backed up"
            fi
            
            # Copy new configuration
            cp -r /tmp/docker-deploy/* /opt/n8n/
            
            # Set proper permissions
            chown -R $USER:$USER /opt/n8n/
            
            # Update n8n version if specified
            if [ "'$N8N_VERSION'" != "latest" ]; then
              sed -i "s|image: n8nio/n8n:latest|image: n8nio/n8n:'$N8N_VERSION'|g" /opt/n8n/docker-compose.yml
              echo "✅ Updated n8n version to '$N8N_VERSION'"
            fi
            
            echo "✅ Docker configuration updated"
          '
          
      - name: Validate environment configuration
        run: |
          echo "🔍 Validating environment configuration..."
          ssh $SERVER_USER@$SERVER_HOST '
            cd /opt/n8n
            
            # Check required environment variables
            if ! grep -q "^N8N_ENCRYPTION_KEY=" .env || grep -q "^N8N_ENCRYPTION_KEY=CHANGE" .env; then
              echo "❌ N8N_ENCRYPTION_KEY not configured"
              echo "Please set a secure encryption key in /opt/n8n/.env"
              exit 1
            fi
            
            if ! grep -q "^DB_POSTGRESDB_PASSWORD=" .env || grep -q "^DB_POSTGRESDB_PASSWORD=CHANGE" .env; then
              echo "❌ DB_POSTGRESDB_PASSWORD not configured"
              echo "Please set a secure database password in /opt/n8n/.env"
              exit 1
            fi
            
            if ! grep -q "^DOMAIN_NAME=" .env || grep -q "^DOMAIN_NAME=yourdomain.com" .env; then
              echo "⚠️ DOMAIN_NAME not configured, using localhost"
              sed -i "s/^DOMAIN_NAME=.*/DOMAIN_NAME=localhost/" .env
            fi
            
            echo "✅ Environment configuration validated"
          '
          
      - name: Run deployment
        run: |
          echo "🚀 Starting n8n deployment..."
          ssh $SERVER_USER@$SERVER_HOST '
            cd /opt/n8n
            
            # Make deployment script executable
            chmod +x /tmp/deploy_n8n.sh
            
            # Run deployment
            if /tmp/deploy_n8n.sh; then
              echo "✅ Deployment completed successfully"
            else
              echo "❌ Deployment failed"
              echo "Checking logs..."
              docker compose logs --tail 50
              exit 1
            fi
            
            # Cleanup
            rm -f /tmp/deploy_n8n.sh
            rm -rf /tmp/docker-deploy
          '
          
      - name: Wait for services
        run: |
          echo "⏳ Waiting for services to be ready..."
          ssh $SERVER_USER@$SERVER_HOST '
            cd /opt/n8n
            
            # Wait for PostgreSQL
            echo "Waiting for PostgreSQL..."
            for i in {1..30}; do
              if docker compose exec postgres pg_isready -U n8n >/dev/null 2>&1; then
                echo "✅ PostgreSQL is ready"
                break
              fi
              echo "Attempt $i/30: PostgreSQL not ready, waiting..."
              sleep 5
            done
            
            # Wait for Redis
            echo "Waiting for Redis..."
            for i in {1..30}; do
              if docker compose exec redis redis-cli ping >/dev/null 2>&1; then
                echo "✅ Redis is ready"
                break
              fi
              echo "Attempt $i/30: Redis not ready, waiting..."
              sleep 5
            done
            
            # Wait for n8n
            echo "Waiting for n8n..."
            for i in {1..60}; do
              if curl -f -s http://localhost:5678/healthz >/dev/null 2>&1; then
                echo "✅ n8n is ready"
                break
              fi
              echo "Attempt $i/60: n8n not ready, waiting..."
              sleep 5
            done
          '
          
      - name: Verify deployment
        run: |
          echo "🔍 Verifying deployment..."
          ssh $SERVER_USER@$SERVER_HOST '
            cd /opt/n8n
            
            echo "=== Container Status ==="
            docker compose ps
            echo ""
            
            echo "=== Health Checks ==="
            
            # Check PostgreSQL
            if docker compose exec postgres pg_isready -U n8n >/dev/null 2>&1; then
              echo "✅ PostgreSQL: Healthy"
            else
              echo "❌ PostgreSQL: Unhealthy"
            fi
            
            # Check Redis
            if docker compose exec redis redis-cli ping >/dev/null 2>&1; then
              echo "✅ Redis: Healthy"
            else
              echo "❌ Redis: Unhealthy"
            fi
            
            # Check n8n
            if curl -f -s http://localhost:5678/healthz >/dev/null 2>&1; then
              echo "✅ n8n: Healthy"
            else
              echo "❌ n8n: Unhealthy"
            fi
            
            echo ""
            echo "=== Resource Usage ==="
            docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"
            
            echo ""
            echo "=== Disk Usage ==="
            df -h /opt/n8n
            
            # Final health check
            if curl -f -s http://localhost:5678/healthz >/dev/null 2>&1; then
              echo ""
              echo "🎉 Deployment verification successful!"
            else
              echo ""
              echo "❌ Deployment verification failed"
              echo "n8n is not responding on http://localhost:5678"
              exit 1
            fi
          '
          
      - name: Display access information
        run: |
          echo "## 🎉 n8n Deployment Complete!"
          echo ""
          echo "Your n8n instance has been deployed successfully."
          echo ""
          echo "### Access Information:"
          echo "- **Local Access:** http://$SERVER_HOST:5678"
          echo "- **SSH Access:** \`ssh $SERVER_USER@$SERVER_HOST\`"
          echo ""
          echo "### Next Steps:"
          echo "1. **Configure SSL/HTTPS:**"
          echo "   - Ensure your domain points to $SERVER_HOST"
          echo "   - Run the 'Configure Proxy' workflow"
          echo ""
          echo "2. **Set up backups:**"
          echo "   - Configure rclone: \`ssh $SERVER_USER@$SERVER_HOST rclone config\`"
          echo "   - Run the 'Backup' workflow"
          echo ""
          echo "3. **Access n8n:**"
          echo "   - Open http://$SERVER_HOST:5678 in your browser"
          echo "   - Create your admin account"
          echo "   - Start building workflows!"
          echo ""
          echo "### Management Commands:"
          echo "- **View logs:** \`ssh $SERVER_USER@$SERVER_HOST 'cd /opt/n8n && docker compose logs -f'\`"
          echo "- **Restart services:** \`ssh $SERVER_USER@$SERVER_HOST 'cd /opt/n8n && docker compose restart'\`"
          echo "- **Update n8n:** Re-run this workflow with a specific version"
          
      - name: Save deployment info
        run: |
          echo "📝 Saving deployment information..."
          
          # Get deployment info from server
          DEPLOYMENT_INFO=$(ssh $SERVER_USER@$SERVER_HOST '
            cd /opt/n8n
            cat <<EOF
            {
              "timestamp": "$(date -Iseconds)",
              "n8n_version": "'$N8N_VERSION'",
              "containers": $(docker compose ps --format json | jq -s .),
              "workflow_run": "'${{ github.run_id }}'",
              "commit": "'${{ github.sha }}'"
            }
          EOF
          ')
          
          echo "$DEPLOYMENT_INFO" > deployment-info.json
          
          # Upload to server
          scp deployment-info.json $SERVER_USER@$SERVER_HOST:/opt/n8n/deployment-info.json
          
          echo "✅ Deployment information saved"
