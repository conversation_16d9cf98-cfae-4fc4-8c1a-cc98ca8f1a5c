name: Restore n8n Data

on:
  workflow_dispatch:
    inputs:
      server_host:
        description: 'Server hostname or IP address'
        required: false
        type: string
      server_user:
        description: 'SSH username'
        required: false
        type: string
        default: 'deployer'
      backup_id:
        description: 'Backup ID to restore (leave empty for latest)'
        required: false
        type: string
        default: 'latest'
      confirm_restore:
        description: 'Type "CONFIRM" to proceed with restore'
        required: true
        type: string

env:
  SERVER_HOST: ${{ inputs.server_host || secrets.SERVER_HOST }}
  SERVER_USER: ${{ inputs.server_user || secrets.SERVER_USER || 'deployer' }}
  BACKUP_ID: ${{ inputs.backup_id || 'latest' }}

jobs:
  validate:
    name: Validate Restore Request
    runs-on: ubuntu-latest
    timeout-minutes: 5
    
    steps:
      - name: Validate confirmation
        run: |
          if [ "${{ inputs.confirm_restore }}" != "CONFIRM" ]; then
            echo "❌ Restore not confirmed"
            echo "You must type 'CONFIRM' to proceed with restore"
            echo "This operation will overwrite existing n8n data!"
            exit 1
          fi
          
          echo "✅ Restore confirmed"
          
      - name: Validate inputs
        run: |
          if [ -z "$SERVER_HOST" ]; then
            echo "❌ SERVER_HOST is required"
            exit 1
          fi
          
          if [ -z "$SERVER_USER" ]; then
            echo "❌ SERVER_USER is required"
            exit 1
          fi
          
          echo "✅ Restore target: $SERVER_USER@$SERVER_HOST"
          echo "✅ Backup ID: $BACKUP_ID"

  restore:
    name: Restore n8n Data
    runs-on: ubuntu-latest
    timeout-minutes: 30
    needs: validate
    environment: production  # Require approval for production restores
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Setup SSH agent
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.SSH_DEPLOY_KEY }}
          
      - name: Add SSH known hosts
        run: |
          if [ -n "${{ secrets.SSH_KNOWN_HOSTS }}" ]; then
            echo "${{ secrets.SSH_KNOWN_HOSTS }}" >> ~/.ssh/known_hosts
          else
            echo "Host *" >> ~/.ssh/config
            echo "  StrictHostKeyChecking no" >> ~/.ssh/config
            echo "  UserKnownHostsFile /dev/null" >> ~/.ssh/config
          fi
          
      - name: Test SSH connection
        run: |
          echo "🔍 Testing SSH connection..."
          ssh -o ConnectTimeout=10 $SERVER_USER@$SERVER_HOST "echo 'SSH connection successful'"
          
      - name: Check restore prerequisites
        run: |
          echo "🔍 Checking restore prerequisites..."
          ssh $SERVER_USER@$SERVER_HOST '
            # Check if n8n is deployed
            if ! [ -f "/opt/n8n/docker-compose.yml" ]; then
              echo "❌ n8n is not deployed"
              exit 1
            fi
            
            # Check if rclone is configured
            if ! command -v rclone >/dev/null 2>&1; then
              echo "❌ rclone is not installed"
              exit 1
            fi
            
            # Check rclone configuration
            if ! rclone listremotes | grep -q "gdrive:"; then
              echo "❌ rclone Google Drive remote not configured"
              exit 1
            fi
            
            # Test rclone connection
            if ! rclone lsd gdrive: >/dev/null 2>&1; then
              echo "❌ Cannot connect to Google Drive"
              exit 1
            fi
            
            echo "✅ Restore prerequisites check passed"
          '
          
      - name: List available backups
        id: list_backups
        run: |
          echo "📋 Listing available backups..."
          
          BACKUP_LIST=$(ssh $SERVER_USER@$SERVER_HOST '
            echo "Available backups in Google Drive:"
            echo "=================================="
            rclone ls gdrive:n8n-backups | sort -k2 -r | head -20
            echo ""
            
            # If specific backup requested, check if it exists
            if [ "'$BACKUP_ID'" != "latest" ]; then
              if rclone lsf gdrive:n8n-backups | grep -q "'$BACKUP_ID'"; then
                echo "✅ Requested backup found: '$BACKUP_ID'"
              else
                echo "❌ Requested backup not found: '$BACKUP_ID'"
                exit 1
              fi
            else
              LATEST=$(rclone lsf gdrive:n8n-backups --max-age 90d | grep "\.tar\.gz$" | sort -r | head -1)
              if [ -n "$LATEST" ]; then
                echo "✅ Latest backup found: $LATEST"
              else
                echo "❌ No recent backups found"
                exit 1
              fi
            fi
          ')
          
          echo "$BACKUP_LIST"
          
      - name: Create pre-restore backup
        run: |
          echo "🗄️ Creating pre-restore backup of current state..."
          ssh $SERVER_USER@$SERVER_HOST '
            cd /opt/n8n
            
            # Create pre-restore backup
            PRE_RESTORE_DIR="/var/backups/n8n/pre-restore-$(date +%Y%m%d-%H%M%S)"
            mkdir -p "$PRE_RESTORE_DIR"
            
            echo "Creating pre-restore backup at: $PRE_RESTORE_DIR"
            
            # Backup current n8n data
            if [ -d "data/n8n" ] && [ "$(ls -A data/n8n)" ]; then
              tar -czf "$PRE_RESTORE_DIR/current-n8n-data.tar.gz" -C data n8n/
              echo "✅ Current n8n data backed up"
            fi
            
            # Backup current database
            if docker compose ps postgres | grep -q "running"; then
              docker compose exec -T postgres pg_dump -U n8n -d n8n | gzip > "$PRE_RESTORE_DIR/current-database.sql.gz"
              echo "✅ Current database backed up"
            fi
            
            # Backup environment file
            if [ -f ".env" ]; then
              cp .env "$PRE_RESTORE_DIR/"
              echo "✅ Environment file backed up"
            fi
            
            echo "✅ Pre-restore backup completed: $PRE_RESTORE_DIR"
          '
          
      - name: Copy restore script
        run: |
          echo "📤 Copying restore script..."
          scp scripts/restore_backup.sh $SERVER_USER@$SERVER_HOST:/tmp/restore_backup.sh
          ssh $SERVER_USER@$SERVER_HOST "chmod +x /tmp/restore_backup.sh"
          
      - name: Stop n8n services
        run: |
          echo "⏹️ Stopping n8n services..."
          ssh $SERVER_USER@$SERVER_HOST '
            cd /opt/n8n
            
            echo "Stopping n8n and nginx..."
            docker compose stop n8n nginx || true
            
            echo "✅ Services stopped"
          '
          
      - name: Perform restore
        id: restore
        run: |
          echo "🔄 Performing restore..."
          
          RESTORE_OUTPUT=$(ssh $SERVER_USER@$SERVER_HOST '
            cd /opt/n8n
            
            # Run restore script
            if /tmp/restore_backup.sh --backup-id "'$BACKUP_ID'" --yes; then
              echo "✅ Restore completed successfully"
              
              # Get restore information
              echo "restore_completed=true"
              echo "restore_timestamp=$(date -Iseconds)"
              
            else
              echo "❌ Restore failed"
              exit 1
            fi
            
            # Cleanup
            rm -f /tmp/restore_backup.sh
          ')
          
          echo "$RESTORE_OUTPUT"
          
          # Extract restore information
          if echo "$RESTORE_OUTPUT" | grep -q "restore_completed=true"; then
            echo "restore_success=true" >> $GITHUB_OUTPUT
          else
            echo "restore_success=false" >> $GITHUB_OUTPUT
          fi
          
      - name: Start services
        run: |
          echo "▶️ Starting services..."
          ssh $SERVER_USER@$SERVER_HOST '
            cd /opt/n8n
            
            # Start all services
            docker compose up -d
            
            echo "✅ Services started"
          '
          
      - name: Wait for services
        run: |
          echo "⏳ Waiting for services to be ready..."
          ssh $SERVER_USER@$SERVER_HOST '
            cd /opt/n8n
            
            # Wait for PostgreSQL
            echo "Waiting for PostgreSQL..."
            for i in {1..30}; do
              if docker compose exec postgres pg_isready -U n8n >/dev/null 2>&1; then
                echo "✅ PostgreSQL is ready"
                break
              fi
              echo "Attempt $i/30: PostgreSQL not ready, waiting..."
              sleep 5
            done
            
            # Wait for n8n
            echo "Waiting for n8n..."
            for i in {1..60}; do
              if curl -f -s http://localhost:5678/healthz >/dev/null 2>&1; then
                echo "✅ n8n is ready"
                break
              fi
              echo "Attempt $i/60: n8n not ready, waiting..."
              sleep 5
            done
          '
          
      - name: Verify restore
        run: |
          echo "🔍 Verifying restore..."
          ssh $SERVER_USER@$SERVER_HOST '
            cd /opt/n8n
            
            echo "=== Service Status ==="
            docker compose ps
            echo ""
            
            echo "=== Health Checks ==="
            
            # Check PostgreSQL
            if docker compose exec postgres pg_isready -U n8n >/dev/null 2>&1; then
              echo "✅ PostgreSQL: Healthy"
            else
              echo "❌ PostgreSQL: Unhealthy"
            fi
            
            # Check n8n
            if curl -f -s http://localhost:5678/healthz >/dev/null 2>&1; then
              echo "✅ n8n: Healthy"
            else
              echo "❌ n8n: Unhealthy"
            fi
            
            echo ""
            echo "=== Database Check ==="
            # Check if database has data
            DB_TABLES=$(docker compose exec -T postgres psql -U n8n -d n8n -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '\''public'\'';" | tr -d " \n")
            echo "Database tables: $DB_TABLES"
            
            if [ "$DB_TABLES" -gt 0 ]; then
              echo "✅ Database contains data"
            else
              echo "⚠️ Database appears empty"
            fi
            
            echo ""
            echo "=== n8n Data Check ==="
            if [ -d "data/n8n" ] && [ "$(ls -A data/n8n)" ]; then
              echo "✅ n8n data directory contains files"
              echo "Files: $(ls -la data/n8n | wc -l) items"
            else
              echo "⚠️ n8n data directory is empty"
            fi
            
            # Final verification
            if curl -f -s http://localhost:5678/healthz >/dev/null 2>&1; then
              echo ""
              echo "🎉 Restore verification successful!"
            else
              echo ""
              echo "❌ Restore verification failed"
              exit 1
            fi
          '
          
      - name: Display restore summary
        if: always()
        run: |
          echo "## 🔄 Restore Summary"
          echo ""
          echo "**Status:** ${{ job.status == 'success' && '✅ Success' || '❌ Failed' }}"
          echo "**Server:** $SERVER_USER@$SERVER_HOST"
          echo "**Backup ID:** $BACKUP_ID"
          echo "**Timestamp:** $(date -Iseconds)"
          echo ""
          
          if [ "${{ job.status }}" = "success" ]; then
            echo "### ✅ Restore Completed Successfully"
            echo ""
            echo "Your n8n instance has been restored from backup."
            echo ""
            echo "**Access Information:**"
            echo "- Local: http://$SERVER_HOST:5678"
            echo "- SSH: \`ssh $SERVER_USER@$SERVER_HOST\`"
            echo ""
            echo "**Important Notes:**"
            echo "- A pre-restore backup was created automatically"
            echo "- All workflows and data have been restored"
            echo "- You may need to reconfigure integrations"
            echo "- Check that all workflows are working as expected"
            
          else
            echo "### ❌ Restore Failed"
            echo ""
            echo "The restore operation failed. Your original data should be intact."
            echo ""
            echo "**Troubleshooting:**"
            echo "- Check backup availability: \`ssh $SERVER_USER@$SERVER_HOST 'rclone ls gdrive:n8n-backups'\`"
            echo "- Verify backup integrity"
            echo "- Check disk space on server"
            echo "- Review restore logs: \`ssh $SERVER_USER@$SERVER_HOST 'tail -f /var/log/n8n/restore.log'\`"
            echo "- Contact support if the issue persists"
          fi
          
          echo ""
          echo "**Next Steps:**"
          echo "- Test your workflows"
          echo "- Verify integrations and credentials"
          echo "- Update any changed configurations"
          echo "- Consider running a backup after verification"
          
      - name: Send notification
        if: always()
        run: |
          # Determine status
          if [ "${{ job.status }}" = "success" ]; then
            STATUS="✅ SUCCESS"
            COLOR="good"
          else
            STATUS="❌ FAILED"
            COLOR="danger"
          fi
          
          # Prepare notification message
          MESSAGE="n8n Restore $STATUS
          
          **Server:** $SERVER_USER@$SERVER_HOST
          **Backup ID:** $BACKUP_ID
          **Time:** $(date -Iseconds)
          **Workflow:** ${{ github.workflow }}
          **Run ID:** ${{ github.run_id }}"
          
          echo "Restore completed with status: $STATUS"
          echo "Message: $MESSAGE"
          
          # Here you could add webhook notifications
          # Example:
          # curl -X POST -H 'Content-type: application/json' \
          #   --data "{\"text\":\"$MESSAGE\"}" \
          #   "${{ secrets.WEBHOOK_URL }}"
