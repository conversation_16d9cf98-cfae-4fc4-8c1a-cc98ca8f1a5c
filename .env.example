# n8n Self-Host Installer Environment Configuration
# Copy this file to .env and fill in your values
# DO NOT commit the .env file to version control

# =============================================================================
# BASIC CONFIGURATION
# =============================================================================

# Domain Configuration
DOMAIN_NAME=yourdomain.com
EMAIL_FOR_LETSENCRYPT=<EMAIL>

# n8n Configuration
N8N_HOST=0.0.0.0
N8N_PORT=5678
N8N_PROTOCOL=https

# IMPORTANT: Generate a secure encryption key and keep it safe!
# This key encrypts sensitive data in n8n. If lost, encrypted data cannot be recovered.
# Generate with: openssl rand -base64 32
N8N_ENCRYPTION_KEY=CHANGE_THIS_TO_A_SECURE_32_CHARACTER_KEY

# =============================================================================
# AUTHENTICATION
# =============================================================================

# Basic Authentication (optional but recommended for initial setup)
# Remove or set to false once you have proper user accounts
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=CHANGE_THIS_PASSWORD

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Database Type (postgres recommended for production)
DB_TYPE=postgresdb

# PostgreSQL Configuration
DB_POSTGRESDB_HOST=postgres
DB_POSTGRESDB_PORT=5432
DB_POSTGRESDB_DATABASE=n8n
DB_POSTGRESDB_USER=n8n
# Generate with: openssl rand -base64 32
DB_POSTGRESDB_PASSWORD=CHANGE_THIS_DB_PASSWORD

# Alternative: SQLite (for testing only)
# DB_TYPE=sqlite
# DB_SQLITE_DATABASE=/home/<USER>/.n8n/database.sqlite

# =============================================================================
# REDIS CONFIGURATION (Optional - for queue mode)
# =============================================================================

# Enable queue mode for better performance with multiple workflows
QUEUE_BULL_REDIS_HOST=redis
QUEUE_BULL_REDIS_PORT=6379
QUEUE_BULL_REDIS_DB=0
# QUEUE_BULL_REDIS_PASSWORD=redis_password_if_needed

# =============================================================================
# EMAIL CONFIGURATION (Optional)
# =============================================================================

# SMTP Configuration for email notifications
# N8N_SMTP_HOST=smtp.gmail.com
# N8N_SMTP_PORT=587
# N8N_SMTP_USER=<EMAIL>
# N8N_SMTP_PASS=your-app-password
# N8N_SMTP_SENDER=<EMAIL>
# N8N_SMTP_SSL=false
# N8N_SMTP_TLS=true

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Security Headers
N8N_SECURE_COOKIE=true
N8N_COOKIE_SAME_SITE_POLICY=strict

# CORS Configuration (adjust as needed)
# N8N_CORS_ORIGIN=https://yourdomain.com

# Webhook Configuration
N8N_WEBHOOK_URL=https://yourdomain.com/

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log Level (error, warn, info, verbose, debug, silly)
N8N_LOG_LEVEL=info

# Log Output (console, file)
N8N_LOG_OUTPUT=console

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# Execution Process (main or own)
# 'own' runs executions in separate processes (recommended for production)
EXECUTIONS_PROCESS=own

# Maximum execution timeout (in seconds)
EXECUTIONS_TIMEOUT=3600

# Maximum number of executions to keep in database
EXECUTIONS_DATA_MAX_AGE=168

# Save execution data (all, save-on-error, save-on-success, none)
EXECUTIONS_DATA_SAVE_ON_ERROR=all
EXECUTIONS_DATA_SAVE_ON_SUCCESS=all
EXECUTIONS_DATA_SAVE_MANUAL_EXECUTIONS=true

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================

# Backup retention (days)
BACKUP_RETENTION_DAYS=30

# Backup schedule (cron format)
BACKUP_SCHEDULE="0 2 * * *"

# rclone remote name (configured separately)
RCLONE_REMOTE=gdrive

# Backup destination path
BACKUP_DESTINATION=n8n-backups

# =============================================================================
# MONITORING CONFIGURATION (Optional)
# =============================================================================

# Enable monitoring
MONITORING_ENABLED=false

# Beszel Configuration
# BESZEL_AGENT_KEY=your-beszel-agent-key
# BESZEL_SERVER_URL=https://your-beszel-server.com

# Uptime-Kuma Configuration
UPTIME_KUMA_ENABLED=false
UPTIME_KUMA_PORT=3001

# =============================================================================
# ADVANCED CONFIGURATION
# =============================================================================

# Node.js Configuration
NODE_ENV=production
NODE_OPTIONS=--max-old-space-size=2048

# Timezone
TZ=UTC

# User and Group IDs (for file permissions)
PUID=1000
PGID=1000

# =============================================================================
# DEVELOPMENT/DEBUG CONFIGURATION
# =============================================================================

# Enable debug mode (set to true for troubleshooting)
DEBUG=false

# Skip SSL verification (NEVER use in production)
NODE_TLS_REJECT_UNAUTHORIZED=1

# =============================================================================
# DOCKER COMPOSE CONFIGURATION
# =============================================================================

# Docker Compose Project Name
COMPOSE_PROJECT_NAME=n8n

# Container restart policy
RESTART_POLICY=unless-stopped

# Network name
NETWORK_NAME=n8n-network

# =============================================================================
# NGINX CONFIGURATION
# =============================================================================

# NGINX Configuration
NGINX_CLIENT_MAX_BODY_SIZE=50M
NGINX_PROXY_READ_TIMEOUT=300
NGINX_PROXY_CONNECT_TIMEOUT=300
NGINX_PROXY_SEND_TIMEOUT=300

# SSL Configuration
SSL_CERTIFICATE_PATH=/etc/letsencrypt/live/${DOMAIN_NAME}/fullchain.pem
SSL_CERTIFICATE_KEY_PATH=/etc/letsencrypt/live/${DOMAIN_NAME}/privkey.pem

# =============================================================================
# NOTES AND WARNINGS
# =============================================================================

# SECURITY NOTES:
# 1. Change ALL default passwords and keys
# 2. Use strong, unique passwords for all services
# 3. Keep the N8N_ENCRYPTION_KEY secure and backed up
# 4. Regularly rotate passwords and keys
# 5. Monitor access logs for suspicious activity

# BACKUP NOTES:
# 1. Test backup and restore procedures regularly
# 2. Store backups in multiple locations
# 3. Encrypt backups for sensitive data
# 4. Document recovery procedures

# PERFORMANCE NOTES:
# 1. Adjust memory limits based on your workflows
# 2. Monitor resource usage and scale as needed
# 3. Use queue mode for high-volume workflows
# 4. Consider using managed database for production

# MAINTENANCE NOTES:
# 1. Keep Docker images updated
# 2. Monitor disk space usage
# 3. Review and rotate logs regularly
# 4. Update SSL certificates (automated with Let's Encrypt)

# =============================================================================
# EXAMPLE VALUES FOR TESTING
# =============================================================================

# For testing purposes, you can use these example values:
# DOMAIN_NAME=n8n.example.com
# EMAIL_FOR_LETSENCRYPT=<EMAIL>
# N8N_BASIC_AUTH_USER=testuser
# N8N_BASIC_AUTH_PASSWORD=testpass123
# DB_POSTGRESDB_PASSWORD=testdbpass123
# N8N_ENCRYPTION_KEY=dGVzdGVuY3J5cHRpb25rZXkxMjM0NTY3ODkw

# WARNING: Never use these example values in production!
