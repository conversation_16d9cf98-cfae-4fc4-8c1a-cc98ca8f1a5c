#!/bin/bash

# n8n Self-Host Installer - Backup Script
# This script creates backups of n8n data and uploads them to Google Drive
# It's designed to be run daily via cron or GitHub Actions

set -euo pipefail

# =============================================================================
# CONFIGURATION
# =============================================================================

SCRIPT_NAME="backup_n8n.sh"
LOG_FILE="/var/log/n8n/backup.log"
N8N_DIR="/opt/n8n"
BACKUP_DIR="/var/backups/n8n"
COMPOSE_FILE="$N8N_DIR/docker-compose.yml"
ENV_FILE="$N8N_DIR/.env"

# Default backup settings
BACKUP_RETENTION_DAYS="${BACKUP_RETENTION_DAYS:-30}"
RCLONE_REMOTE="${RCLONE_REMOTE:-gdrive}"
BACKUP_DESTINATION="${BACKUP_DESTINATION:-n8n-backups}"
COMPRESS_BACKUPS="${COMPRESS_BACKUPS:-true}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================

log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

log_info() {
    log "INFO" "$@"
    echo -e "${BLUE}[INFO]${NC} $*"
}

log_success() {
    log "SUCCESS" "$@"
    echo -e "${GREEN}[SUCCESS]${NC} $*"
}

log_warning() {
    log "WARNING" "$@"
    echo -e "${YELLOW}[WARNING]${NC} $*"
}

log_error() {
    log "ERROR" "$@"
    echo -e "${RED}[ERROR]${NC} $*" >&2
}

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

load_environment() {
    log_info "Loading environment configuration..."
    
    if [[ -f "$ENV_FILE" ]]; then
        set -a
        source "$ENV_FILE"
        set +a
        
        # Override with environment file values
        BACKUP_RETENTION_DAYS="${BACKUP_RETENTION_DAYS:-$BACKUP_RETENTION_DAYS}"
        RCLONE_REMOTE="${RCLONE_REMOTE:-$RCLONE_REMOTE}"
        BACKUP_DESTINATION="${BACKUP_DESTINATION:-$BACKUP_DESTINATION}"
        
        log_success "Environment loaded from $ENV_FILE"
    else
        log_warning "Environment file not found: $ENV_FILE"
    fi
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if n8n is deployed
    if [[ ! -f "$COMPOSE_FILE" ]]; then
        log_error "n8n is not deployed yet"
        exit 1
    fi
    
    # Check if rclone is installed
    if ! command -v rclone &> /dev/null; then
        log_error "rclone is not installed"
        exit 1
    fi
    
    # Check if rclone remote is configured
    if ! rclone listremotes | grep -q "^${RCLONE_REMOTE}:$"; then
        log_error "rclone remote '$RCLONE_REMOTE' is not configured"
        log_error "Please run: rclone config"
        exit 1
    fi
    
    # Test rclone connection
    if ! rclone lsd "${RCLONE_REMOTE}:" > /dev/null 2>&1; then
        log_error "Cannot connect to rclone remote '$RCLONE_REMOTE'"
        log_error "Please check your rclone configuration"
        exit 1
    fi
    
    # Create backup directory
    mkdir -p "$BACKUP_DIR"
    
    log_success "Prerequisites check passed"
}

create_backup_name() {
    local timestamp=$(date '+%Y%m%d-%H%M%S')
    local hostname=$(hostname -s)
    echo "n8n-backup-${hostname}-${timestamp}"
}

backup_database() {
    local backup_name="$1"
    local backup_path="$2"
    
    log_info "Backing up PostgreSQL database..."
    
    cd "$N8N_DIR"
    
    # Get database credentials from environment
    local db_name="${DB_POSTGRESDB_DATABASE:-n8n}"
    local db_user="${DB_POSTGRESDB_USER:-n8n}"
    local db_password="${DB_POSTGRESDB_PASSWORD}"
    
    if [[ -z "$db_password" ]]; then
        log_error "Database password not found in environment"
        return 1
    fi
    
    # Create database dump
    if docker compose exec -T postgres pg_dump -U "$db_user" -d "$db_name" > "$backup_path/database.sql"; then
        log_success "Database backup created"
        
        # Compress database dump if enabled
        if [[ "$COMPRESS_BACKUPS" == "true" ]]; then
            gzip "$backup_path/database.sql"
            log_info "Database backup compressed"
        fi
    else
        log_error "Database backup failed"
        return 1
    fi
}

backup_n8n_data() {
    local backup_name="$1"
    local backup_path="$2"
    
    log_info "Backing up n8n data directory..."
    
    local n8n_data_dir="$N8N_DIR/data/n8n"
    
    if [[ -d "$n8n_data_dir" ]] && [[ "$(ls -A $n8n_data_dir)" ]]; then
        if [[ "$COMPRESS_BACKUPS" == "true" ]]; then
            tar -czf "$backup_path/n8n-data.tar.gz" -C "$N8N_DIR/data" n8n/
            log_success "n8n data backup created and compressed"
        else
            cp -r "$n8n_data_dir" "$backup_path/n8n-data"
            log_success "n8n data backup created"
        fi
    else
        log_warning "n8n data directory is empty or doesn't exist"
        touch "$backup_path/n8n-data-empty"
    fi
}

backup_configuration() {
    local backup_name="$1"
    local backup_path="$2"
    
    log_info "Backing up configuration files..."
    
    # Backup environment file (without secrets)
    if [[ -f "$ENV_FILE" ]]; then
        # Create sanitized version without sensitive data
        grep -v -E "(PASSWORD|KEY|SECRET|TOKEN)" "$ENV_FILE" > "$backup_path/env-sanitized" || true
        log_info "Environment configuration backed up (sanitized)"
    fi
    
    # Backup docker-compose file
    if [[ -f "$COMPOSE_FILE" ]]; then
        cp "$COMPOSE_FILE" "$backup_path/docker-compose.yml"
        log_info "Docker Compose configuration backed up"
    fi
    
    # Backup NGINX configuration
    if [[ -d "$N8N_DIR/nginx" ]]; then
        cp -r "$N8N_DIR/nginx" "$backup_path/"
        log_info "NGINX configuration backed up"
    fi
    
    # Create backup metadata
    cat > "$backup_path/backup-info.json" <<EOF
{
    "backup_name": "$backup_name",
    "timestamp": "$(date -Iseconds)",
    "hostname": "$(hostname)",
    "n8n_version": "$(docker compose exec -T n8n n8n --version 2>/dev/null || echo 'unknown')",
    "backup_type": "full",
    "compressed": $COMPRESS_BACKUPS
}
EOF
    
    log_success "Configuration backup completed"
}

upload_backup() {
    local backup_name="$1"
    local backup_path="$2"
    
    log_info "Uploading backup to Google Drive..."
    
    # Create archive of entire backup
    local archive_name="${backup_name}.tar.gz"
    local archive_path="$BACKUP_DIR/$archive_name"
    
    tar -czf "$archive_path" -C "$BACKUP_DIR" "$backup_name"
    
    # Upload to Google Drive
    if rclone copy "$archive_path" "${RCLONE_REMOTE}:${BACKUP_DESTINATION}/"; then
        log_success "Backup uploaded successfully: $archive_name"
        
        # Remove local archive after successful upload
        rm -f "$archive_path"
        log_info "Local archive removed"
    else
        log_error "Backup upload failed"
        return 1
    fi
}

cleanup_old_backups() {
    log_info "Cleaning up old backups..."
    
    # Clean up local backups
    find "$BACKUP_DIR" -name "n8n-backup-*" -type d -mtime +7 -exec rm -rf {} \; 2>/dev/null || true
    log_info "Old local backups cleaned up (>7 days)"
    
    # Clean up remote backups
    log_info "Cleaning up remote backups older than $BACKUP_RETENTION_DAYS days..."
    
    # Get list of remote backups
    local remote_backups=$(rclone lsf "${RCLONE_REMOTE}:${BACKUP_DESTINATION}/" --max-age "${BACKUP_RETENTION_DAYS}d" 2>/dev/null || true)
    
    if [[ -n "$remote_backups" ]]; then
        echo "$remote_backups" | while read -r backup_file; do
            if [[ -n "$backup_file" ]]; then
                rclone delete "${RCLONE_REMOTE}:${BACKUP_DESTINATION}/$backup_file"
                log_info "Deleted old remote backup: $backup_file"
            fi
        done
    fi
    
    log_success "Old backup cleanup completed"
}

verify_backup() {
    local backup_name="$1"
    local backup_path="$2"
    
    log_info "Verifying backup integrity..."
    
    local verification_failed=false
    
    # Check if backup directory exists and is not empty
    if [[ ! -d "$backup_path" ]] || [[ -z "$(ls -A $backup_path)" ]]; then
        log_error "Backup directory is empty or doesn't exist"
        verification_failed=true
    fi
    
    # Check database backup
    if [[ -f "$backup_path/database.sql" ]] || [[ -f "$backup_path/database.sql.gz" ]]; then
        log_info "Database backup found"
    else
        log_warning "Database backup not found"
    fi
    
    # Check n8n data backup
    if [[ -f "$backup_path/n8n-data.tar.gz" ]] || [[ -d "$backup_path/n8n-data" ]] || [[ -f "$backup_path/n8n-data-empty" ]]; then
        log_info "n8n data backup found"
    else
        log_warning "n8n data backup not found"
    fi
    
    # Check backup metadata
    if [[ -f "$backup_path/backup-info.json" ]]; then
        log_info "Backup metadata found"
    else
        log_warning "Backup metadata not found"
    fi
    
    if [[ "$verification_failed" == "true" ]]; then
        log_error "Backup verification failed"
        return 1
    else
        log_success "Backup verification passed"
        return 0
    fi
}

# =============================================================================
# MAIN BACKUP FUNCTION
# =============================================================================

create_backup() {
    log_info "Starting n8n backup process..."
    log_info "Timestamp: $(date)"
    log_info "Retention: $BACKUP_RETENTION_DAYS days"
    log_info "Remote: $RCLONE_REMOTE:$BACKUP_DESTINATION"
    
    # Generate backup name and path
    local backup_name=$(create_backup_name)
    local backup_path="$BACKUP_DIR/$backup_name"
    
    log_info "Backup name: $backup_name"
    log_info "Backup path: $backup_path"
    
    # Create backup directory
    mkdir -p "$backup_path"
    
    # Perform backup steps
    if ! backup_database "$backup_name" "$backup_path"; then
        log_error "Database backup failed"
        exit 1
    fi
    
    backup_n8n_data "$backup_name" "$backup_path"
    backup_configuration "$backup_name" "$backup_path"
    
    # Verify backup
    if ! verify_backup "$backup_name" "$backup_path"; then
        log_error "Backup verification failed"
        exit 1
    fi
    
    # Upload backup
    if ! upload_backup "$backup_name" "$backup_path"; then
        log_error "Backup upload failed"
        exit 1
    fi
    
    # Clean up old backups
    cleanup_old_backups
    
    # Remove local backup directory
    rm -rf "$backup_path"
    
    log_success "Backup process completed successfully!"
    log_info "Backup uploaded as: ${backup_name}.tar.gz"
}

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================

# Handle command line arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [--help|--test|--list|--cleanup]"
        echo "  --help     Show this help message"
        echo "  --test     Test backup configuration without creating backup"
        echo "  --list     List available backups"
        echo "  --cleanup  Clean up old backups only"
        exit 0
        ;;
    --test)
        echo "Testing backup configuration..."
        load_environment
        check_prerequisites
        echo "Backup configuration test passed"
        exit 0
        ;;
    --list)
        load_environment
        echo "Available backups in ${RCLONE_REMOTE}:${BACKUP_DESTINATION}:"
        rclone ls "${RCLONE_REMOTE}:${BACKUP_DESTINATION}/" | sort -k2
        exit 0
        ;;
    --cleanup)
        load_environment
        cleanup_old_backups
        exit 0
        ;;
esac

# Create log directory if it doesn't exist
mkdir -p "$(dirname "$LOG_FILE")"
touch "$LOG_FILE"

# Load environment and check prerequisites
load_environment
check_prerequisites

# Create backup
create_backup "$@"
