# SSH Known Hosts Example
# This file shows the format for SSH host key verification
# 
# To generate the actual known_hosts content for your server:
# ssh-keyscan -H your-server-ip-or-hostname
# 
# Example output format:
# |1|base64hash==|base64hash== ssh-rsa AAAAB3NzaC1yc2EAAAA...
# |1|base64hash==|base64hash== ecdsa-sha2-nistp256 AAAAE2VjZHNh...
# |1|base64hash==|base64hash== ssh-ed25519 AAAAC3NzaC1lZDI1NTE5...

# =============================================================================
# INSTRUCTIONS FOR GENERATING SSH KNOWN HOSTS
# =============================================================================

# 1. Replace YOUR_SERVER_IP with your actual server IP or hostname
# 2. Run the following command on your local machine:
#    ssh-keyscan -H YOUR_SERVER_IP
# 3. Copy the output to your GitHub Secrets as SSH_KNOWN_HOSTS
# 4. The output will look similar to the examples below

# =============================================================================
# EXAMPLE KNOWN HOSTS ENTRIES
# =============================================================================

# Example for IP address ************:
# |1|abcdef1234567890==|1234567890abcdef== ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC...
# |1|abcdef1234567890==|1234567890abcdef== ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNTY...
# |1|abcdef1234567890==|1234567890abcdef== ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIGq...

# Example for hostname n8n.example.com:
# |1|xyz789012345678==|987654321xyz== ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQD...
# |1|xyz789012345678==|987654321xyz== ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNTY...
# |1|xyz789012345678==|987654321xyz== ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIHr...

# =============================================================================
# SECURITY NOTES
# =============================================================================

# 1. NEVER use the example hashes above - they are not real
# 2. Always generate fresh known_hosts entries for your server
# 3. The -H flag hashes the hostname/IP for additional security
# 4. Verify the fingerprints match your server's actual keys
# 5. Store the known_hosts content securely in GitHub Secrets

# =============================================================================
# VERIFICATION STEPS
# =============================================================================

# To verify your server's SSH fingerprints:
# 1. SSH to your server manually first: ssh user@your-server
# 2. Note the fingerprint shown during first connection
# 3. Compare with the output of: ssh-keyscan your-server
# 4. Ensure they match before adding to GitHub Secrets

# =============================================================================
# TROUBLESHOOTING
# =============================================================================

# If you get "Host key verification failed" errors:
# 1. Regenerate known_hosts with: ssh-keyscan -H your-server-ip
# 2. Update the SSH_KNOWN_HOSTS secret in GitHub
# 3. Ensure the server IP/hostname matches exactly
# 4. Check that SSH is running on the expected port (22)

# If ssh-keyscan fails:
# 1. Verify the server is accessible: ping your-server-ip
# 2. Check SSH service is running: telnet your-server-ip 22
# 3. Verify firewall allows SSH connections on port 22
# 4. Try with explicit port: ssh-keyscan -p 22 your-server-ip

# =============================================================================
# MULTIPLE SERVERS
# =============================================================================

# If you have multiple servers, include all of them:
# ssh-keyscan -H server1.example.com
# ssh-keyscan -H server2.example.com
# ssh-keyscan -H ************
# ssh-keyscan -H ************

# Combine all outputs into a single SSH_KNOWN_HOSTS secret

# =============================================================================
# AUTOMATION SCRIPT
# =============================================================================

# You can create a script to generate known_hosts:
# #!/bin/bash
# SERVER_HOST="your-server-ip-or-hostname"
# echo "Generating known_hosts for $SERVER_HOST..."
# ssh-keyscan -H "$SERVER_HOST" > known_hosts_temp
# echo "Generated known_hosts content:"
# cat known_hosts_temp
# echo ""
# echo "Copy the above content to your GitHub Secret: SSH_KNOWN_HOSTS"
# rm known_hosts_temp

# =============================================================================
# GITHUB SECRETS CONFIGURATION
# =============================================================================

# In your GitHub repository:
# 1. Go to Settings → Secrets and variables → Actions
# 2. Click "New repository secret"
# 3. Name: SSH_KNOWN_HOSTS
# 4. Value: Paste the output from ssh-keyscan -H your-server
# 5. Click "Add secret"

# The GitHub Actions workflow will use this to verify your server's identity
# and prevent man-in-the-middle attacks during deployment.

# =============================================================================
# EXAMPLE COMMAND OUTPUT
# =============================================================================

# $ ssh-keyscan -H ************
# # ************:22 SSH-2.0-OpenSSH_8.9p1 Ubuntu-3ubuntu0.1
# |1|abcdef1234567890==|1234567890abcdef== ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC7...
# |1|abcdef1234567890==|1234567890abcdef== ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNTY...
# |1|abcdef1234567890==|1234567890abcdef== ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIGq4...

# Copy everything starting with |1| to your SSH_KNOWN_HOSTS secret
