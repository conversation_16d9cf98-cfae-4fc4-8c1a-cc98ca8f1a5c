# Custom n8n Dockerfile
# This extends the official n8n image with additional tools and configurations
# Use this if you need custom nodes, tools, or configurations

FROM n8nio/n8n:latest

# Metadata
LABEL maintainer="n8n Self-Host Installer"
LABEL description="Custom n8n image with additional tools and configurations"
LABEL version="1.0.0"

# Switch to root for installations
USER root

# Install additional system packages
RUN apk add --no-cache \
    curl \
    wget \
    git \
    openssh-client \
    rsync \
    jq \
    python3 \
    py3-pip \
    nodejs \
    npm

# Install additional Python packages (if needed for custom nodes)
RUN pip3 install --no-cache-dir \
    requests \
    pandas \
    numpy \
    beautifulsoup4 \
    lxml

# Install additional Node.js packages globally (if needed)
RUN npm install -g \
    axios \
    lodash \
    moment \
    uuid

# Create directories for custom configurations
RUN mkdir -p /opt/custom-nodes \
    && mkdir -p /opt/custom-scripts \
    && mkdir -p /opt/backups

# Copy custom nodes (if any)
# COPY custom-nodes/ /opt/custom-nodes/

# Copy custom scripts (if any)
# COPY scripts/ /opt/custom-scripts/

# Set proper permissions
RUN chown -R node:node /opt/custom-nodes \
    && chown -R node:node /opt/custom-scripts \
    && chown -R node:node /opt/backups

# Switch back to node user
USER node

# Set environment variables for custom configurations
ENV N8N_CUSTOM_EXTENSIONS="/opt/custom-nodes"
ENV N8N_USER_FOLDER="/home/<USER>/.n8n"

# Install custom n8n community nodes (examples)
# Uncomment and modify as needed
# RUN cd /usr/local/lib/node_modules/n8n && npm install n8n-nodes-base-plus
# RUN cd /usr/local/lib/node_modules/n8n && npm install n8n-nodes-google-sheets-plus

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:5678/healthz || exit 1

# Expose port
EXPOSE 5678

# Default command (inherited from base image)
# CMD ["n8n", "start"]

# =============================================================================
# USAGE INSTRUCTIONS
# =============================================================================

# To build this custom image:
# docker build -t n8n-custom:latest -f docker/Dockerfile .

# To use in docker-compose.yml, replace:
# image: n8nio/n8n:latest
# with:
# image: n8n-custom:latest
# build:
#   context: .
#   dockerfile: docker/Dockerfile

# =============================================================================
# CUSTOMIZATION EXAMPLES
# =============================================================================

# Example 1: Add custom node modules
# COPY package.json /tmp/
# RUN cd /tmp && npm install
# RUN cp -r /tmp/node_modules/* /usr/local/lib/node_modules/n8n/node_modules/

# Example 2: Add custom Python scripts
# COPY python-scripts/ /opt/python-scripts/
# RUN chmod +x /opt/python-scripts/*.py

# Example 3: Add custom certificates
# COPY certificates/ /usr/local/share/ca-certificates/
# RUN update-ca-certificates

# Example 4: Add custom fonts (for PDF generation)
# RUN apk add --no-cache ttf-dejavu ttf-liberation

# Example 5: Add database clients
# RUN apk add --no-cache postgresql-client mysql-client

# =============================================================================
# SECURITY CONSIDERATIONS
# =============================================================================

# 1. Always run as non-root user (node)
# 2. Only install necessary packages
# 3. Use specific package versions when possible
# 4. Regularly update base image
# 5. Scan for vulnerabilities

# =============================================================================
# PERFORMANCE OPTIMIZATIONS
# =============================================================================

# 1. Use multi-stage builds for smaller images
# 2. Combine RUN commands to reduce layers
# 3. Clean up package caches
# 4. Use .dockerignore to exclude unnecessary files

# =============================================================================
# DEVELOPMENT VS PRODUCTION
# =============================================================================

# For development, you might want to add:
# - Development tools (vim, nano, htop)
# - Debug utilities
# - Additional logging

# For production, focus on:
# - Minimal image size
# - Security hardening
# - Performance optimization
# - Health checks
