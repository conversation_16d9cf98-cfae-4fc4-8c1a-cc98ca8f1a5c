# Quick Start Guide

Get your n8n instance running in under 30 minutes!

## Prerequisites

- A Ubuntu 22.04 VM with SSH access
- A domain name pointing to your VM's IP address
- A GitHub account
- A Google Drive account (for backups)

## Step 1: Prepare Your VM

### 1.1 Create VM
Create a Ubuntu 22.04 VM with these minimum specs:
- **Testing**: 1 vCPU, 2GB RAM, 20GB disk
- **Production**: 2+ vCPU, 4GB+ RAM, 50GB+ disk

### 1.2 Configure SSH Access
```bash
# On your local machine, generate SSH key if you don't have one
ssh-keygen -t ed25519 -C "<EMAIL>"

# Copy public key to your VM
ssh-copy-id -i ~/.ssh/id_ed25519.pub user@your-vm-ip

# Test SSH access
ssh -i ~/.ssh/id_ed25519 user@your-vm-ip
```

### 1.3 Create Deploy User
```bash
# On your VM, create a dedicated deploy user
sudo adduser deployer
sudo usermod -aG sudo deployer
sudo mkdir -p /home/<USER>/.ssh
sudo cp ~/.ssh/authorized_keys /home/<USER>/.ssh/
sudo chown -R deployer:deployer /home/<USER>/.ssh
sudo chmod 700 /home/<USER>/.ssh
sudo chmod 600 /home/<USER>/.ssh/authorized_keys
```

## Step 2: Configure DNS

Point your domain to your VM's IP address:
```
A    yourdomain.com    -> YOUR_VM_IP
A    *.yourdomain.com  -> YOUR_VM_IP  (optional, for subdomains)
```

Wait for DNS propagation (use `dig yourdomain.com` to verify).

## Step 3: Set Up Google Drive Backup

### 3.1 Install rclone on your VM
```bash
# SSH to your VM as deployer user
ssh deployer@your-vm-ip

# Install rclone
curl https://rclone.org/install.sh | sudo bash

# Configure Google Drive
rclone config
# Choose: n (new remote)
# Name: gdrive
# Storage: drive (Google Drive)
# Follow the OAuth flow
```

### 3.2 Test rclone
```bash
# Create test directory and file
mkdir -p /tmp/test-backup
echo "test" > /tmp/test-backup/test.txt

# Upload test file
rclone copy /tmp/test-backup gdrive:n8n-backups-test

# Verify upload
rclone ls gdrive:n8n-backups-test

# Clean up test
rclone delete gdrive:n8n-backups-test
rm -rf /tmp/test-backup
```

## Step 4: Fork and Configure Repository

### 4.1 Fork Repository
1. Fork this repository to your GitHub account
2. Clone your fork locally

### 4.2 Configure GitHub Secrets
Go to your repository → Settings → Secrets and variables → Actions

Add these secrets:

| Secret Name | Description | Example |
|-------------|-------------|---------|
| `SSH_DEPLOY_KEY` | Private SSH key content | Contents of `~/.ssh/id_ed25519` |
| `SERVER_HOST` | VM IP or hostname | `************` or `vm.yourdomain.com` |
| `SERVER_USER` | Deploy user name | `deployer` |
| `SSH_KNOWN_HOSTS` | SSH host verification | Run: `ssh-keyscan your-vm-ip` |
| `DOMAIN_NAME` | Your domain name | `yourdomain.com` |
| `EMAIL_FOR_LETSENCRYPT` | Email for SSL certificates | `<EMAIL>` |

### 4.3 Get SSH Known Hosts
```bash
# On your local machine
ssh-keyscan your-vm-ip

# Copy the output to SSH_KNOWN_HOSTS secret
```

## Step 5: Deploy n8n

### 5.1 Run Combined Setup Workflow
1. Go to your repository → Actions
2. Select "Combined Setup" workflow
3. Click "Run workflow"
4. Fill in the inputs:
   - **Domain Name**: `yourdomain.com`
   - **Email for Let's Encrypt**: `<EMAIL>`
   - **Install Monitoring**: `true` (optional)
5. Click "Run workflow"

### 5.2 Monitor Deployment
Watch the workflow progress. It will:
1. ✅ Set up VM with Docker and dependencies
2. ✅ Deploy n8n with PostgreSQL and Redis
3. ✅ Configure NGINX reverse proxy
4. ✅ Obtain SSL certificate from Let's Encrypt
5. ✅ Set up automated backups
6. ✅ Install monitoring (if selected)

## Step 6: Access Your n8n Instance

### 6.1 First Login
1. Open `https://yourdomain.com` in your browser
2. You should see the n8n setup screen
3. Create your admin account
4. Start building workflows!

### 6.2 Import Sample Workflows
1. In n8n, go to Workflows → Import from File
2. Import the sample workflows from the `workflows/` directory:
   - `volunteer_signup.json`
   - `incident_report.json`
   - `donation_tracking.json`

## Step 7: Test Backup and Restore

### 7.1 Manual Backup
1. Go to Actions → "Backup n8n Data"
2. Click "Run workflow"
3. Verify backup appears in your Google Drive

### 7.2 Test Restore (Optional)
⚠️ **Warning**: This will overwrite your current n8n data!

1. Go to Actions → "Restore n8n Data"
2. Click "Run workflow"
3. Enter backup ID or leave empty for latest
4. Confirm restoration

## Troubleshooting

### Common Issues

**SSH Connection Failed**
- Verify SSH key is correct in secrets
- Check SSH_KNOWN_HOSTS matches your server
- Ensure deployer user has SSH access

**SSL Certificate Failed**
- Verify domain points to your VM IP
- Check ports 80 and 443 are open
- Ensure no other web server is running

**n8n Not Accessible**
- Check Docker containers are running: `docker ps`
- Verify NGINX configuration: `sudo nginx -t`
- Check logs: `docker-compose logs n8n`

**Backup Failed**
- Verify rclone is configured correctly
- Test rclone manually: `rclone ls gdrive:`
- Check Google Drive permissions

### Getting Help

1. Check the [Issues](../../issues) page
2. Review logs in GitHub Actions
3. SSH to your VM and check Docker logs
4. Join the [Discussions](../../discussions)

## Next Steps

- Set up monitoring alerts in n8n
- Configure additional backup destinations
- Explore n8n's extensive node library
- Set up webhooks for external integrations
- Configure SMTP for email notifications

## Security Checklist

- [ ] SSH key authentication only (no passwords)
- [ ] Firewall configured (UFW enabled)
- [ ] SSL certificate installed and auto-renewing
- [ ] Regular backups running
- [ ] Deploy user has minimal privileges
- [ ] n8n admin account secured with strong password
- [ ] Regular security updates enabled

---

🎉 **Congratulations!** Your n8n instance is now running securely with automated backups and monitoring!
