#!/bin/bash

# n8n Self-Host Installer - Reverse Proxy Configuration Script
# This script configures NGINX reverse proxy with Let's Encrypt SSL
# It's designed to be idempotent - safe to run multiple times

set -euo pipefail

# =============================================================================
# CONFIGURATION
# =============================================================================

SCRIPT_NAME="configure_proxy.sh"
LOG_FILE="/var/log/n8n/proxy.log"
N8N_DIR="/opt/n8n"
NGINX_DIR="$N8N_DIR/nginx"
COMPOSE_FILE="$N8N_DIR/docker-compose.yml"
ENV_FILE="$N8N_DIR/.env"

# Default values (can be overridden by environment or arguments)
DOMAIN_NAME="${DOMAIN_NAME:-}"
EMAIL_FOR_LETSENCRYPT="${EMAIL_FOR_LETSENCRYPT:-}"
STAGING="${STAGING:-false}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================

log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

log_info() {
    log "INFO" "$@"
    echo -e "${BLUE}[INFO]${NC} $*"
}

log_success() {
    log "SUCCESS" "$@"
    echo -e "${GREEN}[SUCCESS]${NC} $*"
}

log_warning() {
    log "WARNING" "$@"
    echo -e "${YELLOW}[WARNING]${NC} $*"
}

log_error() {
    log "ERROR" "$@"
    echo -e "${RED}[ERROR]${NC} $*" >&2
}

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

load_environment() {
    log_info "Loading environment configuration..."
    
    if [[ -f "$ENV_FILE" ]]; then
        # Source environment file
        set -a
        source "$ENV_FILE"
        set +a
        
        # Override with environment file values if not set
        DOMAIN_NAME="${DOMAIN_NAME:-$DOMAIN_NAME}"
        EMAIL_FOR_LETSENCRYPT="${EMAIL_FOR_LETSENCRYPT:-$EMAIL_FOR_LETSENCRYPT}"
        
        log_success "Environment loaded from $ENV_FILE"
    else
        log_warning "Environment file not found: $ENV_FILE"
    fi
}

validate_inputs() {
    log_info "Validating inputs..."
    
    if [[ -z "$DOMAIN_NAME" ]]; then
        log_error "DOMAIN_NAME is required"
        log_error "Set it in $ENV_FILE or pass as argument: --domain yourdomain.com"
        exit 1
    fi
    
    if [[ -z "$EMAIL_FOR_LETSENCRYPT" ]]; then
        log_error "EMAIL_FOR_LETSENCRYPT is required"
        log_error "Set it in $ENV_FILE or pass as argument: --email <EMAIL>"
        exit 1
    fi
    
    # Validate domain format
    if [[ ! "$DOMAIN_NAME" =~ ^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$ ]]; then
        log_error "Invalid domain name format: $DOMAIN_NAME"
        exit 1
    fi
    
    # Validate email format
    if [[ ! "$EMAIL_FOR_LETSENCRYPT" =~ ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$ ]]; then
        log_error "Invalid email format: $EMAIL_FOR_LETSENCRYPT"
        exit 1
    fi
    
    log_success "Input validation passed"
    log_info "Domain: $DOMAIN_NAME"
    log_info "Email: $EMAIL_FOR_LETSENCRYPT"
    log_info "Staging: $STAGING"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if n8n is deployed
    if [[ ! -f "$COMPOSE_FILE" ]]; then
        log_error "n8n is not deployed yet"
        log_error "Please run deploy_n8n.sh first"
        exit 1
    fi
    
    # Check if n8n is running
    cd "$N8N_DIR"
    if ! docker compose ps n8n | grep -q "running"; then
        log_error "n8n container is not running"
        log_error "Please start n8n first: docker compose up -d"
        exit 1
    fi
    
    # Check if certbot is installed
    if ! command -v certbot &> /dev/null; then
        log_error "Certbot is not installed"
        log_error "Please run setup_vm.sh first"
        exit 1
    fi
    
    # Check DNS resolution
    log_info "Checking DNS resolution for $DOMAIN_NAME..."
    local server_ip=$(curl -s ifconfig.me || curl -s ipinfo.io/ip || echo "unknown")
    local domain_ip=$(dig +short "$DOMAIN_NAME" | tail -n1)
    
    if [[ -z "$domain_ip" ]]; then
        log_warning "DNS resolution failed for $DOMAIN_NAME"
        log_warning "Make sure your domain points to this server: $server_ip"
    elif [[ "$domain_ip" != "$server_ip" ]]; then
        log_warning "DNS mismatch: $DOMAIN_NAME resolves to $domain_ip, but server IP is $server_ip"
        log_warning "SSL certificate generation may fail"
    else
        log_success "DNS resolution correct: $DOMAIN_NAME -> $domain_ip"
    fi
    
    log_success "Prerequisites check completed"
}

setup_nginx_config() {
    log_info "Setting up NGINX configuration..."
    
    # Create NGINX directories
    mkdir -p "$NGINX_DIR/conf.d"
    mkdir -p "/var/www/certbot"
    
    # Process the template file
    local template_file="$NGINX_DIR/conf.d/n8n.conf.template"
    local config_file="$NGINX_DIR/conf.d/n8n.conf"
    
    if [[ -f "$template_file" ]]; then
        # Replace variables in template
        sed "s/\${DOMAIN_NAME}/$DOMAIN_NAME/g" "$template_file" > "$config_file"
        log_success "NGINX configuration created from template"
    else
        # Create basic configuration
        cat > "$config_file" <<EOF
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name $DOMAIN_NAME;
    
    ssl_certificate /etc/letsencrypt/live/$DOMAIN_NAME/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN_NAME/privkey.pem;
    
    location / {
        proxy_pass http://n8n:5678;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF
        log_success "Basic NGINX configuration created"
    fi
    
    # Validate NGINX configuration
    if docker run --rm -v "$NGINX_DIR/nginx.conf:/etc/nginx/nginx.conf:ro" -v "$NGINX_DIR/conf.d:/etc/nginx/conf.d:ro" nginx:alpine nginx -t; then
        log_success "NGINX configuration is valid"
    else
        log_error "NGINX configuration is invalid"
        exit 1
    fi
}

obtain_ssl_certificate() {
    log_info "Obtaining SSL certificate from Let's Encrypt..."
    
    # Prepare certbot command
    local certbot_args=(
        "certonly"
        "--webroot"
        "--webroot-path=/var/www/certbot"
        "--email" "$EMAIL_FOR_LETSENCRYPT"
        "--agree-tos"
        "--no-eff-email"
        "-d" "$DOMAIN_NAME"
    )
    
    # Add staging flag if requested
    if [[ "$STAGING" == "true" ]]; then
        certbot_args+=("--staging")
        log_info "Using Let's Encrypt staging environment"
    fi
    
    # Check if certificate already exists
    if [[ -f "/etc/letsencrypt/live/$DOMAIN_NAME/fullchain.pem" ]]; then
        log_info "SSL certificate already exists for $DOMAIN_NAME"
        
        # Check if certificate is valid and not expiring soon
        local cert_expiry=$(openssl x509 -enddate -noout -in "/etc/letsencrypt/live/$DOMAIN_NAME/fullchain.pem" | cut -d= -f2)
        local expiry_timestamp=$(date -d "$cert_expiry" +%s)
        local current_timestamp=$(date +%s)
        local days_until_expiry=$(( (expiry_timestamp - current_timestamp) / 86400 ))
        
        if [[ $days_until_expiry -gt 30 ]]; then
            log_success "Certificate is valid for $days_until_expiry more days"
            return 0
        else
            log_warning "Certificate expires in $days_until_expiry days, renewing..."
        fi
    fi
    
    # Stop NGINX temporarily to avoid port conflicts
    cd "$N8N_DIR"
    if docker compose ps nginx | grep -q "running"; then
        log_info "Stopping NGINX temporarily for certificate generation..."
        docker compose stop nginx
    fi
    
    # Obtain certificate
    if sudo certbot "${certbot_args[@]}"; then
        log_success "SSL certificate obtained successfully"
    else
        log_error "Failed to obtain SSL certificate"
        log_error "Please check:"
        log_error "1. Domain $DOMAIN_NAME points to this server"
        log_error "2. Ports 80 and 443 are open"
        log_error "3. No other web server is running"
        exit 1
    fi
    
    # Set proper permissions
    sudo chmod -R 755 /etc/letsencrypt/live/
    sudo chmod -R 755 /etc/letsencrypt/archive/
}

setup_auto_renewal() {
    log_info "Setting up automatic certificate renewal..."
    
    # Create renewal script
    local renewal_script="/usr/local/bin/renew-n8n-cert.sh"
    sudo tee "$renewal_script" > /dev/null <<EOF
#!/bin/bash
# Automatic certificate renewal for n8n

set -euo pipefail

LOG_FILE="/var/log/n8n/cert-renewal.log"
N8N_DIR="$N8N_DIR"

log() {
    echo "[\$(date '+%Y-%m-%d %H:%M:%S')] \$*" | tee -a "\$LOG_FILE"
}

log "Starting certificate renewal check..."

# Renew certificates
if certbot renew --quiet --webroot --webroot-path=/var/www/certbot; then
    log "Certificate renewal check completed"
    
    # Reload NGINX if certificates were renewed
    if [[ -f "\$N8N_DIR/docker-compose.yml" ]]; then
        cd "\$N8N_DIR"
        if docker compose ps nginx | grep -q "running"; then
            docker compose exec nginx nginx -s reload
            log "NGINX configuration reloaded"
        fi
    fi
else
    log "Certificate renewal failed"
    exit 1
fi
EOF
    
    sudo chmod +x "$renewal_script"
    
    # Create systemd timer for renewal
    sudo tee "/etc/systemd/system/n8n-cert-renewal.service" > /dev/null <<EOF
[Unit]
Description=Renew n8n SSL certificates
After=network.target

[Service]
Type=oneshot
ExecStart=$renewal_script
User=root
EOF
    
    sudo tee "/etc/systemd/system/n8n-cert-renewal.timer" > /dev/null <<EOF
[Unit]
Description=Run n8n SSL certificate renewal twice daily
Requires=n8n-cert-renewal.service

[Timer]
OnCalendar=*-*-* 00,12:00:00
RandomizedDelaySec=3600
Persistent=true

[Install]
WantedBy=timers.target
EOF
    
    # Enable and start timer
    sudo systemctl daemon-reload
    sudo systemctl enable n8n-cert-renewal.timer
    sudo systemctl start n8n-cert-renewal.timer
    
    log_success "Automatic certificate renewal configured"
    log_info "Renewal will run twice daily at 00:00 and 12:00"
}

start_nginx() {
    log_info "Starting NGINX reverse proxy..."
    
    cd "$N8N_DIR"
    
    # Start NGINX
    docker compose up -d nginx
    
    # Wait for NGINX to start
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if docker compose ps nginx | grep -q "running"; then
            log_success "NGINX is running"
            break
        fi
        
        log_info "Waiting for NGINX to start... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    if [[ $attempt -gt $max_attempts ]]; then
        log_error "NGINX failed to start"
        docker compose logs nginx
        exit 1
    fi
}

test_https() {
    log_info "Testing HTTPS configuration..."
    
    # Wait a moment for everything to settle
    sleep 5
    
    # Test HTTPS connection
    if curl -f -s -I "https://$DOMAIN_NAME" > /dev/null; then
        log_success "HTTPS is working correctly"
    else
        log_error "HTTPS test failed"
        log_error "Please check NGINX logs: docker compose logs nginx"
        exit 1
    fi
    
    # Test SSL certificate
    local cert_info=$(echo | openssl s_client -servername "$DOMAIN_NAME" -connect "$DOMAIN_NAME:443" 2>/dev/null | openssl x509 -noout -subject -dates)
    log_info "SSL Certificate info:"
    echo "$cert_info"
}

# =============================================================================
# MAIN CONFIGURATION FUNCTION
# =============================================================================

configure_proxy() {
    log_info "Starting reverse proxy configuration..."
    log_info "Timestamp: $(date)"
    log_info "Domain: $DOMAIN_NAME"
    log_info "Email: $EMAIL_FOR_LETSENCRYPT"
    
    # Configuration steps
    load_environment
    validate_inputs
    check_prerequisites
    setup_nginx_config
    obtain_ssl_certificate
    setup_auto_renewal
    start_nginx
    test_https
    
    log_success "Reverse proxy configuration completed successfully!"
    log_info "n8n is now available at: https://$DOMAIN_NAME"
    log_info "SSL certificate will auto-renew twice daily"
}

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --domain)
            DOMAIN_NAME="$2"
            shift 2
            ;;
        --email)
            EMAIL_FOR_LETSENCRYPT="$2"
            shift 2
            ;;
        --staging)
            STAGING="true"
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [options]"
            echo "Options:"
            echo "  --domain DOMAIN    Domain name for SSL certificate"
            echo "  --email EMAIL      Email for Let's Encrypt notifications"
            echo "  --staging          Use Let's Encrypt staging environment"
            echo "  --help             Show this help message"
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Create log directory if it doesn't exist
mkdir -p "$(dirname "$LOG_FILE")"
touch "$LOG_FILE"

# Run configuration
configure_proxy "$@"
