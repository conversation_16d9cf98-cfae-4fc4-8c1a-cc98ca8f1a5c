# n8n Self-Host Installer

A production-ready, one-click deployment solution for n8n using GitHub Actions, Docker, and SSH automation.

## 🚀 Quick Start

1. **Fork this repository**
2. **Set up your VM** (Ubuntu 22.04 recommended)
3. **Configure GitHub Secrets** (see [QUICKSTART.md](QUICKSTART.md))
4. **Run the combined setup workflow**

## 📋 Features

- **One-click deployment** via GitHub Actions
- **Production-ready** with NGINX reverse proxy and Let's Encrypt SSL
- **Automated backups** to Google Drive with rclone
- **Restore functionality** for disaster recovery
- **Optional monitoring** with Beszel and Uptime-Kuma
- **Idempotent scripts** - safe to run multiple times
- **Security-focused** with proper secret management

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   GitHub        │    │   Your VM       │    │   Google Drive  │
│   Actions       │───▶│   - n8n         │───▶│   Backups       │
│   Workflows     │    │   - PostgreSQL  │    │                 │
└─────────────────┘    │   - Redis       │    └─────────────────┘
                       │   - NGINX       │
                       │   - Let's Encrypt│
                       └─────────────────┘
```

## 📦 What's Included

### Core Components
- **n8n** - Workflow automation platform
- **PostgreSQL** - Database for n8n data
- **Redis** - Optional queue backend
- **NGINX** - Reverse proxy with SSL termination
- **Let's Encrypt** - Automatic SSL certificates

### Scripts
- `scripts/setup_vm.sh` - VM provisioning and Docker setup
- `scripts/deploy_n8n.sh` - n8n deployment and updates
- `scripts/configure_proxy.sh` - NGINX and SSL configuration
- `scripts/backup_n8n.sh` - Automated backups to Google Drive
- `scripts/restore_backup.sh` - Backup restoration
- `scripts/install_beszel.sh` - Optional monitoring agent
- `scripts/deploy_uptime_kuma.sh` - Optional uptime monitoring

### GitHub Actions Workflows
- `setup-vm.yml` - Initial VM setup
- `deploy-n8n.yml` - Deploy/update n8n
- `backup.yml` - Daily backup automation
- `restore.yml` - Manual backup restoration
- `combined-setup.yml` - Complete deployment pipeline

## 🔧 VM Requirements

### Minimum (Testing)
- 1 vCPU, 2GB RAM, 20GB disk
- Ubuntu 22.04 LTS
- SSH access with key authentication

### Recommended (Production)
- 2+ vCPU, 4GB+ RAM, 50GB+ disk
- Ubuntu 22.04 LTS
- Managed PostgreSQL (optional)

### Network Requirements
- SSH (port 22) - for deployment
- HTTP (port 80) - for Let's Encrypt challenges
- HTTPS (port 443) - for n8n access

## 🔐 Security Features

- SSH key-based authentication only
- Host key verification
- Least privilege deployment user
- Encrypted backup storage
- No secrets in repository
- Automatic security updates

## 📚 Documentation

- [QUICKSTART.md](QUICKSTART.md) - Step-by-step setup guide
- [SECURITY.md](SECURITY.md) - Security considerations and best practices
- [PRIVACY.md](PRIVACY.md) - Data handling and privacy information
- [CONTRIBUTING.md](CONTRIBUTING.md) - How to contribute to this project

## 🎯 Sample Workflows

This repository includes three sample n8n workflows to get you started:

1. **Volunteer Signup** - Process volunteer applications
2. **Incident Report** - Handle incident reporting and notifications
3. **Donation Tracking** - Track and acknowledge donations

## 🔄 Backup & Restore

- **Automated daily backups** to Google Drive
- **30-day retention** by default (configurable)
- **One-click restore** via GitHub Actions
- **Encrypted backup storage** with rclone

## 📊 Optional Monitoring

- **Beszel Agent** - System metrics collection
- **Uptime-Kuma** - Uptime monitoring and alerting
- **Integration** with n8n workflows for custom alerts

## 🚨 Support

- Check [Issues](../../issues) for common problems
- Review [Discussions](../../discussions) for community help
- See [SECURITY.md](SECURITY.md) for security-related issues

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [n8n](https://n8n.io/) - The amazing workflow automation platform
- [Docker](https://docker.com/) - Containerization platform
- [Let's Encrypt](https://letsencrypt.org/) - Free SSL certificates
- [rclone](https://rclone.org/) - Cloud storage sync tool

---

**⚠️ Important**: This installer is designed for single-server deployments. For high-availability or multi-server setups, consider using Kubernetes or Docker Swarm configurations.
