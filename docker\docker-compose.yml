version: '3.8'

# n8n Self-Host Installer - Docker Compose Configuration
# This file defines all services needed for a production n8n deployment

services:
  # =============================================================================
  # PostgreSQL Database
  # =============================================================================
  postgres:
    image: postgres:15-alpine
    container_name: n8n_postgres
    restart: ${RESTART_POLICY:-unless-stopped}
    environment:
      POSTGRES_DB: ${DB_POSTGRESDB_DATABASE:-n8n}
      POSTGRES_USER: ${DB_POSTGRESDB_USER:-n8n}
      POSTGRES_PASSWORD: ${DB_POSTGRESDB_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgres-init:/docker-entrypoint-initdb.d:ro
    networks:
      - n8n-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_POSTGRESDB_USER:-n8n} -d ${DB_POSTGRESDB_DATABASE:-n8n}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # =============================================================================
  # Redis (Optional - for queue mode)
  # =============================================================================
  redis:
    image: redis:7-alpine
    container_name: n8n_redis
    restart: ${RESTART_POLICY:-unless-stopped}
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - n8n-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # =============================================================================
  # n8n Application
  # =============================================================================
  n8n:
    image: n8nio/n8n:latest
    container_name: n8n_app
    restart: ${RESTART_POLICY:-unless-stopped}
    environment:
      # Database Configuration
      DB_TYPE: ${DB_TYPE:-postgresdb}
      DB_POSTGRESDB_HOST: postgres
      DB_POSTGRESDB_PORT: ${DB_POSTGRESDB_PORT:-5432}
      DB_POSTGRESDB_DATABASE: ${DB_POSTGRESDB_DATABASE:-n8n}
      DB_POSTGRESDB_USER: ${DB_POSTGRESDB_USER:-n8n}
      DB_POSTGRESDB_PASSWORD: ${DB_POSTGRESDB_PASSWORD}
      
      # Redis Configuration (for queue mode)
      QUEUE_BULL_REDIS_HOST: redis
      QUEUE_BULL_REDIS_PORT: ${QUEUE_BULL_REDIS_PORT:-6379}
      QUEUE_BULL_REDIS_DB: ${QUEUE_BULL_REDIS_DB:-0}
      
      # n8n Configuration
      N8N_HOST: ${N8N_HOST:-0.0.0.0}
      N8N_PORT: ${N8N_PORT:-5678}
      N8N_PROTOCOL: ${N8N_PROTOCOL:-https}
      N8N_ENCRYPTION_KEY: ${N8N_ENCRYPTION_KEY}
      
      # Authentication
      N8N_BASIC_AUTH_ACTIVE: ${N8N_BASIC_AUTH_ACTIVE:-false}
      N8N_BASIC_AUTH_USER: ${N8N_BASIC_AUTH_USER:-}
      N8N_BASIC_AUTH_PASSWORD: ${N8N_BASIC_AUTH_PASSWORD:-}
      
      # Webhook Configuration
      WEBHOOK_URL: ${N8N_WEBHOOK_URL:-https://${DOMAIN_NAME}/}
      
      # Security
      N8N_SECURE_COOKIE: ${N8N_SECURE_COOKIE:-true}
      N8N_COOKIE_SAME_SITE_POLICY: ${N8N_COOKIE_SAME_SITE_POLICY:-strict}
      
      # Logging
      N8N_LOG_LEVEL: ${N8N_LOG_LEVEL:-info}
      N8N_LOG_OUTPUT: ${N8N_LOG_OUTPUT:-console}
      
      # Execution Configuration
      EXECUTIONS_PROCESS: ${EXECUTIONS_PROCESS:-own}
      EXECUTIONS_TIMEOUT: ${EXECUTIONS_TIMEOUT:-3600}
      EXECUTIONS_DATA_MAX_AGE: ${EXECUTIONS_DATA_MAX_AGE:-168}
      EXECUTIONS_DATA_SAVE_ON_ERROR: ${EXECUTIONS_DATA_SAVE_ON_ERROR:-all}
      EXECUTIONS_DATA_SAVE_ON_SUCCESS: ${EXECUTIONS_DATA_SAVE_ON_SUCCESS:-all}
      EXECUTIONS_DATA_SAVE_MANUAL_EXECUTIONS: ${EXECUTIONS_DATA_SAVE_MANUAL_EXECUTIONS:-true}
      
      # Node.js Configuration
      NODE_ENV: ${NODE_ENV:-production}
      NODE_OPTIONS: ${NODE_OPTIONS:---max-old-space-size=2048}
      
      # Timezone
      TZ: ${TZ:-UTC}
      
      # SMTP Configuration (optional)
      N8N_SMTP_HOST: ${N8N_SMTP_HOST:-}
      N8N_SMTP_PORT: ${N8N_SMTP_PORT:-}
      N8N_SMTP_USER: ${N8N_SMTP_USER:-}
      N8N_SMTP_PASS: ${N8N_SMTP_PASS:-}
      N8N_SMTP_SENDER: ${N8N_SMTP_SENDER:-}
      N8N_SMTP_SSL: ${N8N_SMTP_SSL:-false}
      N8N_SMTP_TLS: ${N8N_SMTP_TLS:-true}
    ports:
      - "127.0.0.1:5678:5678"  # Only bind to localhost
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - /var/run/docker.sock:/var/run/docker.sock:ro  # For Docker nodes (optional)
    networks:
      - n8n-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:5678/healthz || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"

  # =============================================================================
  # NGINX Reverse Proxy
  # =============================================================================
  nginx:
    image: nginx:alpine
    container_name: n8n_nginx
    restart: ${RESTART_POLICY:-unless-stopped}
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - /etc/letsencrypt:/etc/letsencrypt:ro
      - /var/www/certbot:/var/www/certbot:ro
      - nginx_logs:/var/log/nginx
    networks:
      - n8n-network
    depends_on:
      - n8n
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/nginx-health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # =============================================================================
  # Uptime-Kuma (Optional Monitoring)
  # =============================================================================
  uptime-kuma:
    image: louislam/uptime-kuma:1
    container_name: n8n_uptime_kuma
    restart: ${RESTART_POLICY:-unless-stopped}
    ports:
      - "127.0.0.1:3001:3001"  # Only bind to localhost
    volumes:
      - uptime_kuma_data:/app/data
    networks:
      - n8n-network
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3001 || exit 1"]
      interval: 60s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    profiles:
      - monitoring

# =============================================================================
# Networks
# =============================================================================
networks:
  n8n-network:
    name: ${NETWORK_NAME:-n8n-network}
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# =============================================================================
# Volumes
# =============================================================================
volumes:
  postgres_data:
    name: n8n_postgres_data
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/n8n/data/postgres

  redis_data:
    name: n8n_redis_data
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/n8n/data/redis

  n8n_data:
    name: n8n_app_data
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/n8n/data/n8n

  uptime_kuma_data:
    name: n8n_uptime_kuma_data
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/n8n/data/uptime-kuma

  nginx_logs:
    name: n8n_nginx_logs
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/log/nginx

# =============================================================================
# Additional Services (Profiles)
# =============================================================================

# To start with monitoring:
# docker compose --profile monitoring up -d

# To start without monitoring:
# docker compose up -d

# Available profiles:
# - monitoring: Includes Uptime-Kuma and other monitoring tools
# - development: Includes development tools and debug containers
