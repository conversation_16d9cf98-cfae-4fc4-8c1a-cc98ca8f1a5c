#!/bin/bash

# n8n Self-Host Installer - Uptime-<PERSON><PERSON> Deployment Script
# This script deploys Uptime-<PERSON><PERSON> monitoring service alongside n8n
# Uptime-<PERSON><PERSON> provides uptime monitoring and alerting

set -euo pipefail

# =============================================================================
# CONFIGURATION
# =============================================================================

SCRIPT_NAME="deploy_uptime_kuma.sh"
LOG_FILE="/var/log/n8n/uptime-kuma.log"
N8N_DIR="/opt/n8n"
COMPOSE_FILE="$N8N_DIR/docker-compose.yml"
ENV_FILE="$N8N_DIR/.env"

# Default configuration
UPTIME_KUMA_PORT="${UPTIME_KUMA_PORT:-3001}"
UPTIME_KUMA_ENABLED="${UPTIME_KUMA_ENABLED:-true}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================

log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

log_info() {
    log "INFO" "$@"
    echo -e "${BLUE}[INFO]${NC} $*"
}

log_success() {
    log "SUCCESS" "$@"
    echo -e "${GREEN}[SUCCESS]${NC} $*"
}

log_warning() {
    log "WARNING" "$@"
    echo -e "${YELLOW}[WARNING]${NC} $*"
}

log_error() {
    log "ERROR" "$@"
    echo -e "${RED}[ERROR]${NC} $*" >&2
}

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if n8n is deployed
    if [[ ! -f "$COMPOSE_FILE" ]]; then
        log_error "n8n is not deployed yet"
        log_error "Please run deploy_n8n.sh first"
        exit 1
    fi
    
    # Check if Docker Compose is available
    if ! docker compose version &> /dev/null && ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not available"
        exit 1
    fi
    
    # Check if n8n directory exists
    if [[ ! -d "$N8N_DIR" ]]; then
        log_error "n8n directory not found: $N8N_DIR"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

load_environment() {
    log_info "Loading environment configuration..."
    
    if [[ -f "$ENV_FILE" ]]; then
        set -a
        source "$ENV_FILE"
        set +a
        
        UPTIME_KUMA_PORT="${UPTIME_KUMA_PORT:-$UPTIME_KUMA_PORT}"
        UPTIME_KUMA_ENABLED="${UPTIME_KUMA_ENABLED:-$UPTIME_KUMA_ENABLED}"
        
        log_success "Environment loaded from $ENV_FILE"
    else
        log_warning "Environment file not found: $ENV_FILE"
    fi
}

setup_uptime_kuma_data() {
    log_info "Setting up Uptime-Kuma data directory..."
    
    local data_dir="$N8N_DIR/data/uptime-kuma"
    
    # Create data directory
    mkdir -p "$data_dir"
    
    # Set proper permissions
    chmod 755 "$data_dir"
    
    log_success "Uptime-Kuma data directory created: $data_dir"
}

start_uptime_kuma() {
    log_info "Starting Uptime-Kuma service..."
    
    cd "$N8N_DIR"
    
    # Start Uptime-Kuma with monitoring profile
    if docker compose version &> /dev/null; then
        docker compose --profile monitoring up -d uptime-kuma
    else
        docker-compose --profile monitoring up -d uptime-kuma
    fi
    
    log_success "Uptime-Kuma service started"
}

wait_for_uptime_kuma() {
    log_info "Waiting for Uptime-Kuma to be ready..."
    
    local max_attempts=60
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f -s "http://localhost:$UPTIME_KUMA_PORT" > /dev/null 2>&1; then
            log_success "Uptime-Kuma is ready"
            return 0
        fi
        
        log_info "Attempt $attempt/$max_attempts: Uptime-Kuma not ready yet, waiting..."
        sleep 5
        ((attempt++))
    done
    
    log_error "Uptime-Kuma failed to start within timeout"
    return 1
}

configure_nginx_proxy() {
    log_info "Configuring NGINX proxy for Uptime-Kuma..."
    
    local nginx_config="$N8N_DIR/nginx/conf.d/uptime-kuma.conf"
    local domain_name="${DOMAIN_NAME:-localhost}"
    
    # Create NGINX configuration for Uptime-Kuma
    cat > "$nginx_config" <<EOF
# Uptime-Kuma NGINX Configuration
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name uptime.$domain_name;
    
    # SSL Configuration (if certificates exist)
    ssl_certificate /etc/letsencrypt/live/$domain_name/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$domain_name/privkey.pem;
    
    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    
    # Logging
    access_log /var/log/nginx/uptime_kuma_access.log main;
    error_log /var/log/nginx/uptime_kuma_error.log warn;
    
    # Basic Authentication (optional)
    # auth_basic "Uptime Monitoring";
    # auth_basic_user_file /etc/nginx/.htpasswd;
    
    location / {
        proxy_pass http://uptime-kuma:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        
        # Timeout settings
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }
}

# HTTP redirect
server {
    listen 80;
    listen [::]:80;
    server_name uptime.$domain_name;
    
    location / {
        return 301 https://\$host\$request_uri;
    }
}
EOF
    
    log_success "NGINX configuration created for Uptime-Kuma"
    log_info "Uptime-Kuma will be available at: https://uptime.$domain_name"
    log_warning "Make sure to create DNS record: uptime.$domain_name -> server IP"
}

create_default_monitors() {
    log_info "Creating default monitoring configuration..."
    
    # Create a configuration script for default monitors
    cat > "$N8N_DIR/uptime-kuma-setup.js" <<'EOF'
// Uptime-Kuma Default Monitors Setup
// This script can be used to programmatically set up monitors

const monitors = [
    {
        name: "n8n Application",
        type: "http",
        url: "https://localhost/healthz",
        interval: 60,
        retryInterval: 60,
        maxRetries: 3,
        upsideDown: false,
        ignoreTls: false,
        maxRedirects: 10,
        acceptedStatusCodes: ["200-299"],
        description: "n8n application health check"
    },
    {
        name: "NGINX Proxy",
        type: "http",
        url: "http://localhost/nginx-health",
        interval: 60,
        retryInterval: 60,
        maxRetries: 3,
        upsideDown: false,
        ignoreTls: false,
        maxRedirects: 10,
        acceptedStatusCodes: ["200-299"],
        description: "NGINX reverse proxy health check"
    },
    {
        name: "PostgreSQL Database",
        type: "postgres",
        hostname: "localhost",
        port: 5432,
        database: process.env.DB_POSTGRESDB_DATABASE || "n8n",
        username: process.env.DB_POSTGRESDB_USER || "n8n",
        interval: 300,
        retryInterval: 60,
        maxRetries: 3,
        description: "PostgreSQL database connectivity"
    },
    {
        name: "Redis Cache",
        type: "redis",
        hostname: "localhost",
        port: 6379,
        interval: 300,
        retryInterval: 60,
        maxRetries: 3,
        description: "Redis cache connectivity"
    }
];

console.log("Default monitors configuration:");
console.log(JSON.stringify(monitors, null, 2));
console.log("\nTo set up these monitors:");
console.log("1. Access Uptime-Kuma web interface");
console.log("2. Create monitors manually using the above configuration");
console.log("3. Or use the Uptime-Kuma API to automate setup");
EOF
    
    log_success "Default monitors configuration created"
    log_info "Configuration file: $N8N_DIR/uptime-kuma-setup.js"
}

setup_notifications() {
    log_info "Setting up notification templates..."
    
    # Create notification setup guide
    cat > "$N8N_DIR/uptime-kuma-notifications.md" <<'EOF'
# Uptime-Kuma Notification Setup

## Available Notification Methods

### Email (SMTP)
- SMTP Host: Your email provider's SMTP server
- Port: 587 (TLS) or 465 (SSL)
- Username: Your email address
- Password: Your email password or app password

### Discord Webhook
1. Create a webhook in your Discord server
2. Copy the webhook URL
3. Add as Discord notification in Uptime-Kuma

### Slack Webhook
1. Create a Slack app and webhook
2. Copy the webhook URL
3. Add as Slack notification in Uptime-Kuma

### Telegram Bot
1. Create a Telegram bot via @BotFather
2. Get bot token and chat ID
3. Add as Telegram notification in Uptime-Kuma

### n8n Webhook Integration
1. Create a webhook workflow in n8n
2. Use the webhook URL as a custom notification
3. Process alerts and route to multiple channels

## Example n8n Webhook Workflow

```json
{
  "name": "Uptime Alert Handler",
  "nodes": [
    {
      "name": "Webhook",
      "type": "n8n-nodes-base.webhook",
      "parameters": {
        "path": "uptime-alert",
        "httpMethod": "POST"
      }
    },
    {
      "name": "Process Alert",
      "type": "n8n-nodes-base.function",
      "parameters": {
        "functionCode": "// Process Uptime-Kuma alert\nconst alert = items[0].json;\nreturn [{\n  json: {\n    service: alert.monitorName,\n    status: alert.status,\n    message: alert.msg,\n    timestamp: new Date().toISOString()\n  }\n}];"
      }
    },
    {
      "name": "Send Email",
      "type": "n8n-nodes-base.emailSend",
      "parameters": {
        "subject": "Alert: {{$json.service}} is {{$json.status}}",
        "text": "{{$json.message}}"
      }
    }
  ]
}
```
EOF
    
    log_success "Notification setup guide created"
    log_info "Guide file: $N8N_DIR/uptime-kuma-notifications.md"
}

show_status() {
    log_info "Uptime-Kuma deployment status:"
    echo ""
    echo "=== Service Status ==="
    cd "$N8N_DIR"
    if docker compose version &> /dev/null; then
        docker compose ps uptime-kuma
    else
        docker-compose ps uptime-kuma
    fi
    
    echo ""
    echo "=== Access Information ==="
    echo "Local Access: http://localhost:$UPTIME_KUMA_PORT"
    if [[ -n "${DOMAIN_NAME:-}" ]]; then
        echo "Public Access: https://uptime.$DOMAIN_NAME (requires DNS setup)"
    fi
    
    echo ""
    echo "=== Configuration Files ==="
    echo "Data Directory: $N8N_DIR/data/uptime-kuma"
    echo "NGINX Config: $N8N_DIR/nginx/conf.d/uptime-kuma.conf"
    echo "Setup Script: $N8N_DIR/uptime-kuma-setup.js"
    echo "Notifications Guide: $N8N_DIR/uptime-kuma-notifications.md"
    
    echo ""
    echo "=== Container Logs ==="
    if docker compose version &> /dev/null; then
        docker compose logs uptime-kuma --tail 10
    else
        docker-compose logs uptime-kuma --tail 10
    fi
}

# =============================================================================
# MAIN DEPLOYMENT FUNCTION
# =============================================================================

deploy_uptime_kuma() {
    log_info "Starting Uptime-Kuma deployment..."
    log_info "Timestamp: $(date)"
    log_info "Port: $UPTIME_KUMA_PORT"
    
    # Deployment steps
    check_prerequisites
    load_environment
    setup_uptime_kuma_data
    start_uptime_kuma
    
    # Wait for service to be ready
    if ! wait_for_uptime_kuma; then
        log_error "Uptime-Kuma deployment failed"
        show_status
        exit 1
    fi
    
    # Additional configuration
    configure_nginx_proxy
    create_default_monitors
    setup_notifications
    
    # Show final status
    show_status
    
    log_success "Uptime-Kuma deployment completed successfully!"
    log_info "Access Uptime-Kuma at: http://localhost:$UPTIME_KUMA_PORT"
    log_info "Set up your first admin account and configure monitors"
    log_info "See setup guides in: $N8N_DIR/uptime-kuma-*.{js,md}"
}

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================

# Handle command line arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [--help|--status|--stop|--restart|--logs]"
        echo "  --help     Show this help message"
        echo "  --status   Show current status"
        echo "  --stop     Stop Uptime-Kuma service"
        echo "  --restart  Restart Uptime-Kuma service"
        echo "  --logs     Show service logs"
        exit 0
        ;;
    --status)
        cd "$N8N_DIR" 2>/dev/null || { echo "Uptime-Kuma not deployed yet"; exit 1; }
        show_status
        exit 0
        ;;
    --stop)
        cd "$N8N_DIR" 2>/dev/null || { echo "Uptime-Kuma not deployed yet"; exit 1; }
        log_info "Stopping Uptime-Kuma..."
        if docker compose version &> /dev/null; then
            docker compose stop uptime-kuma
        else
            docker-compose stop uptime-kuma
        fi
        log_success "Uptime-Kuma stopped"
        exit 0
        ;;
    --restart)
        cd "$N8N_DIR" 2>/dev/null || { echo "Uptime-Kuma not deployed yet"; exit 1; }
        log_info "Restarting Uptime-Kuma..."
        if docker compose version &> /dev/null; then
            docker compose restart uptime-kuma
        else
            docker-compose restart uptime-kuma
        fi
        log_success "Uptime-Kuma restarted"
        exit 0
        ;;
    --logs)
        cd "$N8N_DIR" 2>/dev/null || { echo "Uptime-Kuma not deployed yet"; exit 1; }
        if docker compose version &> /dev/null; then
            docker compose logs -f uptime-kuma
        else
            docker-compose logs -f uptime-kuma
        fi
        exit 0
        ;;
esac

# Create log directory if it doesn't exist
mkdir -p "$(dirname "$LOG_FILE")"
touch "$LOG_FILE"

# Run deployment
deploy_uptime_kuma "$@"
