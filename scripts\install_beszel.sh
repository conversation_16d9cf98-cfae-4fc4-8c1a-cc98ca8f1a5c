#!/bin/bash

# n8n Self-Host Installer - Beszel Agent Installation Script
# This script installs and configures Beszel monitoring agent
# Beszel is a lightweight server monitoring tool

set -euo pipefail

# =============================================================================
# CONFIGURATION
# =============================================================================

SCRIPT_NAME="install_beszel.sh"
LOG_FILE="/var/log/n8n/beszel.log"
BESZEL_DIR="/opt/beszel"
BESZEL_VERSION="${BESZEL_VERSION:-latest}"
BESZEL_PORT="${BESZEL_PORT:-45876}"

# Default configuration
BESZEL_SERVER_URL="${BESZEL_SERVER_URL:-}"
BESZEL_AGENT_KEY="${BESZEL_AGENT_KEY:-}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================

log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

log_info() {
    log "INFO" "$@"
    echo -e "${BLUE}[INFO]${NC} $*"
}

log_success() {
    log "SUCCESS" "$@"
    echo -e "${GREEN}[SUCCESS]${NC} $*"
}

log_warning() {
    log "WARNING" "$@"
    echo -e "${YELLOW}[WARNING]${NC} $*"
}

log_error() {
    log "ERROR" "$@"
    echo -e "${RED}[ERROR]${NC} $*" >&2
}

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if running as root or with sudo
    if [[ $EUID -ne 0 ]] && ! sudo -n true 2>/dev/null; then
        log_error "This script requires root privileges or sudo access"
        exit 1
    fi
    
    # Check system architecture
    local arch=$(uname -m)
    case $arch in
        x86_64)
            BESZEL_ARCH="amd64"
            ;;
        aarch64|arm64)
            BESZEL_ARCH="arm64"
            ;;
        armv7l)
            BESZEL_ARCH="armv7"
            ;;
        *)
            log_error "Unsupported architecture: $arch"
            exit 1
            ;;
    esac
    
    log_info "Architecture: $arch -> $BESZEL_ARCH"
    
    # Check internet connectivity
    if ! ping -c 1 ******* &> /dev/null; then
        log_error "No internet connectivity"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

validate_configuration() {
    log_info "Validating configuration..."
    
    # Load from environment file if available
    local env_file="/opt/n8n/.env"
    if [[ -f "$env_file" ]]; then
        set -a
        source "$env_file"
        set +a
        
        BESZEL_SERVER_URL="${BESZEL_SERVER_URL:-$BESZEL_SERVER_URL}"
        BESZEL_AGENT_KEY="${BESZEL_AGENT_KEY:-$BESZEL_AGENT_KEY}"
    fi
    
    # Validate required configuration
    if [[ -z "$BESZEL_SERVER_URL" ]]; then
        log_error "BESZEL_SERVER_URL is required"
        log_error "Set it in environment or pass as argument: --server-url https://your-beszel-server.com"
        exit 1
    fi
    
    if [[ -z "$BESZEL_AGENT_KEY" ]]; then
        log_error "BESZEL_AGENT_KEY is required"
        log_error "Set it in environment or pass as argument: --agent-key your-agent-key"
        exit 1
    fi
    
    # Validate URL format
    if [[ ! "$BESZEL_SERVER_URL" =~ ^https?:// ]]; then
        log_error "Invalid server URL format: $BESZEL_SERVER_URL"
        exit 1
    fi
    
    log_success "Configuration validation passed"
    log_info "Server URL: $BESZEL_SERVER_URL"
    log_info "Agent Key: ${BESZEL_AGENT_KEY:0:8}..."
}

download_beszel() {
    log_info "Downloading Beszel agent..."
    
    # Create directory
    sudo mkdir -p "$BESZEL_DIR"
    
    # Determine download URL
    if [[ "$BESZEL_VERSION" == "latest" ]]; then
        local download_url="https://github.com/henrygd/beszel/releases/latest/download/beszel-agent_linux_${BESZEL_ARCH}"
    else
        local download_url="https://github.com/henrygd/beszel/releases/download/v${BESZEL_VERSION}/beszel-agent_linux_${BESZEL_ARCH}"
    fi
    
    log_info "Download URL: $download_url"
    
    # Download binary
    if sudo curl -L -o "$BESZEL_DIR/beszel-agent" "$download_url"; then
        log_success "Beszel agent downloaded"
    else
        log_error "Failed to download Beszel agent"
        exit 1
    fi
    
    # Make executable
    sudo chmod +x "$BESZEL_DIR/beszel-agent"
    
    # Verify download
    if "$BESZEL_DIR/beszel-agent" --version; then
        log_success "Beszel agent verified"
    else
        log_error "Beszel agent verification failed"
        exit 1
    fi
}

create_configuration() {
    log_info "Creating Beszel configuration..."
    
    # Create configuration file
    sudo tee "$BESZEL_DIR/config.yaml" > /dev/null <<EOF
# Beszel Agent Configuration
server_url: "$BESZEL_SERVER_URL"
agent_key: "$BESZEL_AGENT_KEY"
port: $BESZEL_PORT

# Monitoring settings
interval: 30s
timeout: 10s

# System monitoring
cpu: true
memory: true
disk: true
network: true
processes: true

# Docker monitoring (if available)
docker: true

# Custom metrics
custom_metrics:
  - name: "n8n_status"
    command: "curl -f -s http://localhost:5678/healthz > /dev/null && echo 1 || echo 0"
    interval: 60s
  - name: "nginx_status"
    command: "curl -f -s http://localhost/nginx-health > /dev/null && echo 1 || echo 0"
    interval: 60s

# Logging
log_level: "info"
log_file: "/var/log/beszel-agent.log"
EOF
    
    log_success "Configuration file created"
}

create_systemd_service() {
    log_info "Creating systemd service..."
    
    # Create systemd service file
    sudo tee "/etc/systemd/system/beszel-agent.service" > /dev/null <<EOF
[Unit]
Description=Beszel Monitoring Agent
Documentation=https://github.com/henrygd/beszel
After=network.target
Wants=network.target

[Service]
Type=simple
User=root
Group=root
ExecStart=$BESZEL_DIR/beszel-agent --config $BESZEL_DIR/config.yaml
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=beszel-agent

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log /tmp $BESZEL_DIR

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
EOF
    
    # Reload systemd
    sudo systemctl daemon-reload
    
    log_success "Systemd service created"
}

configure_firewall() {
    log_info "Configuring firewall..."
    
    # Check if UFW is active
    if sudo ufw status | grep -q "Status: active"; then
        # Allow Beszel agent port (usually not needed as it's outbound only)
        # sudo ufw allow $BESZEL_PORT/tcp
        log_info "UFW is active, no additional rules needed for Beszel agent"
    else
        log_info "UFW is not active, skipping firewall configuration"
    fi
    
    log_success "Firewall configuration completed"
}

start_service() {
    log_info "Starting Beszel agent service..."
    
    # Enable and start service
    sudo systemctl enable beszel-agent
    sudo systemctl start beszel-agent
    
    # Wait for service to start
    sleep 5
    
    # Check service status
    if sudo systemctl is-active --quiet beszel-agent; then
        log_success "Beszel agent service is running"
    else
        log_error "Beszel agent service failed to start"
        log_error "Check logs: sudo journalctl -u beszel-agent -f"
        exit 1
    fi
    
    # Show service status
    sudo systemctl status beszel-agent --no-pager
}

test_connection() {
    log_info "Testing connection to Beszel server..."
    
    # Wait a moment for the agent to connect
    sleep 10
    
    # Check logs for connection status
    if sudo journalctl -u beszel-agent --since "1 minute ago" | grep -q "connected\|authenticated"; then
        log_success "Successfully connected to Beszel server"
    else
        log_warning "Connection status unclear, check logs:"
        log_warning "sudo journalctl -u beszel-agent -f"
    fi
}

show_status() {
    log_info "Beszel agent status:"
    echo ""
    echo "=== Service Status ==="
    sudo systemctl status beszel-agent --no-pager
    echo ""
    echo "=== Configuration ==="
    echo "Server URL: $BESZEL_SERVER_URL"
    echo "Agent Port: $BESZEL_PORT"
    echo "Config File: $BESZEL_DIR/config.yaml"
    echo "Log File: /var/log/beszel-agent.log"
    echo ""
    echo "=== Recent Logs ==="
    sudo journalctl -u beszel-agent --since "5 minutes ago" --no-pager | tail -10
}

# =============================================================================
# MAIN INSTALLATION FUNCTION
# =============================================================================

install_beszel() {
    log_info "Starting Beszel agent installation..."
    log_info "Timestamp: $(date)"
    log_info "Version: $BESZEL_VERSION"
    log_info "Architecture: $BESZEL_ARCH"
    
    # Installation steps
    check_prerequisites
    validate_configuration
    download_beszel
    create_configuration
    create_systemd_service
    configure_firewall
    start_service
    test_connection
    
    # Show final status
    show_status
    
    log_success "Beszel agent installation completed successfully!"
    log_info "The agent is now monitoring this server and sending data to: $BESZEL_SERVER_URL"
    log_info "To view logs: sudo journalctl -u beszel-agent -f"
    log_info "To restart: sudo systemctl restart beszel-agent"
}

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --server-url)
            BESZEL_SERVER_URL="$2"
            shift 2
            ;;
        --agent-key)
            BESZEL_AGENT_KEY="$2"
            shift 2
            ;;
        --port)
            BESZEL_PORT="$2"
            shift 2
            ;;
        --version)
            BESZEL_VERSION="$2"
            shift 2
            ;;
        --status)
            show_status
            exit 0
            ;;
        --uninstall)
            log_info "Uninstalling Beszel agent..."
            sudo systemctl stop beszel-agent || true
            sudo systemctl disable beszel-agent || true
            sudo rm -f /etc/systemd/system/beszel-agent.service
            sudo rm -rf "$BESZEL_DIR"
            sudo systemctl daemon-reload
            log_success "Beszel agent uninstalled"
            exit 0
            ;;
        --help|-h)
            echo "Usage: $0 [options]"
            echo "Options:"
            echo "  --server-url URL   Beszel server URL"
            echo "  --agent-key KEY    Agent authentication key"
            echo "  --port PORT        Agent port (default: 45876)"
            echo "  --version VERSION  Beszel version (default: latest)"
            echo "  --status           Show current status"
            echo "  --uninstall        Remove Beszel agent"
            echo "  --help             Show this help message"
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Create log directory if it doesn't exist
mkdir -p "$(dirname "$LOG_FILE")"
touch "$LOG_FILE"

# Run installation
install_beszel "$@"
