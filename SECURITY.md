# Security Policy

## Supported Versions

We provide security updates for the following versions:

| Version | Supported          |
| ------- | ------------------ |
| 1.x.x   | :white_check_mark: |
| < 1.0   | :x:                |

## Security Features

### Authentication & Authorization
- **SSH Key Authentication**: Password authentication is disabled by default
- **Least Privilege**: Deploy user has minimal required permissions
- **Host Key Verification**: SSH connections verify host keys to prevent MITM attacks
- **n8n Basic Auth**: Optional basic authentication for n8n web interface

### Network Security
- **Firewall Configuration**: UFW firewall with minimal open ports (22, 80, 443)
- **SSL/TLS Encryption**: Automatic Let's Encrypt certificates with HTTPS redirect
- **Reverse Proxy**: NGINX acts as a security layer in front of n8n
- **Port Isolation**: Internal services only accessible through reverse proxy

### Data Protection
- **Encryption at Rest**: n8n data encrypted with N8N_ENCRYPTION_KEY
- **Backup Encryption**: rclone encrypts backups before uploading to Google Drive
- **Secret Management**: Sensitive data stored in GitHub Secrets, not in repository
- **Database Security**: PostgreSQL with password authentication and isolated network

### Infrastructure Security
- **Container Isolation**: All services run in Docker containers
- **Regular Updates**: Automated security updates for Ubuntu packages
- **Log Management**: Centralized logging with log rotation
- **Resource Limits**: Docker containers have resource constraints

## Security Best Practices

### Before Deployment

1. **Generate Strong SSH Keys**
   ```bash
   # Use Ed25519 keys (recommended)
   ssh-keygen -t ed25519 -b 4096 -C "<EMAIL>"
   
   # Or RSA if Ed25519 not supported
   ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
   ```

2. **Secure Your VM**
   - Use a reputable cloud provider
   - Enable automatic security updates
   - Configure proper firewall rules
   - Use private networking when possible

3. **Domain Security**
   - Use a dedicated subdomain for n8n
   - Enable DNSSEC if supported by your registrar
   - Consider using Cloudflare for additional DDoS protection

### After Deployment

1. **Change Default Credentials**
   - Set strong n8n admin password
   - Rotate SSH keys regularly
   - Update database passwords

2. **Monitor Your Instance**
   - Enable monitoring with Beszel/Uptime-Kuma
   - Set up alerts for failed logins
   - Monitor disk space and resource usage

3. **Regular Maintenance**
   - Apply security updates monthly
   - Rotate backup encryption keys annually
   - Review access logs regularly

### GitHub Repository Security

1. **Protect Secrets**
   - Never commit secrets to the repository
   - Use GitHub Secrets for sensitive data
   - Enable secret scanning

2. **Branch Protection**
   - Require pull request reviews
   - Enable status checks
   - Restrict who can push to main branch

3. **Workflow Security**
   - Use specific action versions (not @main)
   - Limit workflow permissions
   - Use environments for production deployments

## Reporting Security Vulnerabilities

We take security vulnerabilities seriously. If you discover a security issue, please follow these steps:

### For Critical Vulnerabilities
- **DO NOT** create a public GitHub issue
- Email security details to: [<EMAIL>]
- Include detailed reproduction steps
- Allow 90 days for responsible disclosure

### For Non-Critical Issues
- Create a GitHub issue with the "security" label
- Provide detailed information about the vulnerability
- Suggest potential fixes if possible

### What to Include
- Description of the vulnerability
- Steps to reproduce the issue
- Potential impact assessment
- Suggested mitigation strategies
- Your contact information

## Security Checklist

### Pre-Deployment
- [ ] SSH keys generated and secured
- [ ] VM hardened and updated
- [ ] Firewall rules configured
- [ ] Domain DNS configured
- [ ] GitHub Secrets properly set

### Post-Deployment
- [ ] SSL certificate obtained and valid
- [ ] n8n accessible only via HTTPS
- [ ] Backup system tested and working
- [ ] Monitoring alerts configured
- [ ] Access logs reviewed

### Ongoing Maintenance
- [ ] Security updates applied monthly
- [ ] Backup integrity verified quarterly
- [ ] Access logs reviewed monthly
- [ ] SSH keys rotated annually
- [ ] SSL certificates auto-renewing

## Known Security Considerations

### Limitations
- Single server deployment (no high availability)
- Backup encryption depends on rclone configuration
- GitHub Actions runners are shared infrastructure
- Let's Encrypt rate limits may affect certificate renewal

### Mitigations
- Regular backups to multiple locations
- Monitoring and alerting for service availability
- Use of GitHub Environments for additional protection
- Backup certificate renewal methods

## Compliance

This deployment configuration considers:
- **GDPR**: Data processing transparency and user rights
- **SOC 2**: Security controls and monitoring
- **ISO 27001**: Information security management
- **NIST**: Cybersecurity framework alignment

Note: Full compliance requires additional organizational controls beyond this technical implementation.

## Security Updates

We will publish security updates through:
- GitHub Security Advisories
- Release notes with security fixes
- Email notifications for critical issues
- Community discussions for general security topics

## Contact

For security-related questions or concerns:
- Security Email: [<EMAIL>]
- GitHub Issues: Use "security" label
- Community: GitHub Discussions

---

**Remember**: Security is a shared responsibility. This installer provides a secure foundation, but ongoing security depends on proper configuration, regular updates, and following security best practices.
